# متطلبات مشروع وكيل إنتاج الفيديو بالذكاء الاصطناعي

# Core dependencies
requests>=2.31.0
python-dotenv>=1.0.0
pathlib>=1.0.1

# Google Cloud and AI
google-cloud-aiplatform>=1.38.0
google-auth>=2.23.0
google-auth-oauthlib>=1.1.0
google-auth-httplib2>=0.1.1
google-generativeai>=0.3.0

# Audio/Video processing
moviepy>=1.0.3
pydub>=0.25.1
librosa>=0.10.1
soundfile>=0.12.1

# Web scraping and automation (for account management)
selenium>=4.15.0
beautifulsoup4>=4.12.2
webdriver-manager>=4.0.1

# Data handling
pandas>=2.1.0
numpy>=1.24.0
pillow>=10.0.0

# HTTP and API clients
httpx>=0.25.0
aiohttp>=3.8.0

# Logging and monitoring
loguru>=0.7.0

# Development and testing
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0

# Optional: For advanced features
# opencv-python>=4.8.0  # For video analysis
# torch>=2.0.0          # For ML models
# transformers>=4.30.0  # For advanced NLP

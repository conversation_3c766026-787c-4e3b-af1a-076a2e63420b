# إعداد Gemini API للمشروع

## الحصول على API Key

### الطريقة الأولى: Google AI Studio (مجاني)
1. اذهب إلى: https://aistudio.google.com/
2. سجل الدخول بحساب Google
3. انقر على "Get API Key"
4. انشئ مشروع جديد أو اختر مشروع موجود
5. انسخ API Key

### الطريقة الثانية: Google Cloud Console
1. اذهب إلى: https://console.cloud.google.com/
2. انشئ مشروع جديد أو اختر مشروع موجود
3. فعل Generative AI API
4. انشئ Service Account وحمل JSON key

## إعداد Credentials في N8N

### خطوات الإعداد:
1. افتح N8N على http://localhost:5678
2. اذه<PERSON> إلى Settings > Credentials
3. انقر على "Add Credential"
4. اختر "Google Generative AI API"
5. أدخل API Key

### معلومات الـ Credential:
```json
{
  "name": "Gemini API",
  "type": "googleGenerativeAiApi",
  "data": {
    "apiKey": "YOUR_API_KEY_HERE"
  }
}
```

## نماذج Gemini المتاحة:
- `gemini-2.5-pro`: الأحدث والأقوى (مدفوع)
- `gemini-1.5-pro`: قوي ومجاني مع حدود
- `gemini-1.5-flash`: سريع ومجاني

## حدود الاستخدام المجاني:
- 15 طلب في الدقيقة
- 1 مليون token في اليوم
- 1500 طلب في اليوم

## أمثلة على الـ Prompts:

### لتوليد العناوين:
```
أنشئ عنوان جذاب باللغة العربية لفيديو TikTok يظهر تقطيع [ITEM] بالسكين. 
العنوان يجب أن يكون:
- قصير (أقل من 50 حرف)
- جذاب ومثير للفضول
- يحتوي على إيموجي مناسب
```

### لتوليد الهاشتاقات:
```
أنشئ 10 هاشتاقات مناسبة لفيديو تقطيع [ITEM] على TikTok:
- 5 هاشتاقات بالعربية
- 5 هاشتاقات بالإنجليزية
- تركز على الترند والانتشار
```

## استكشاف الأخطاء:

### خطأ 401 - Unauthorized:
- تأكد من صحة API Key
- تأكد من تفعيل Generative AI API

### خطأ 429 - Rate Limit:
- انتظر دقيقة وأعد المحاولة
- قلل عدد الطلبات

### خطأ 400 - Bad Request:
- تأكد من صحة الـ prompt
- تأكد من اختيار النموذج الصحيح

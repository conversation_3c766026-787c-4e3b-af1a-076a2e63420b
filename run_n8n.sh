#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8

echo ""
echo "========================================"
echo "           مدير n8n الرئيسي"
echo "========================================"
echo ""

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python غير مثبت. يرجى تثبيت Python أولاً."
        echo "Ubuntu/Debian: sudo apt install python3 python3-pip"
        echo "CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "macOS: brew install python3"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ Python متوفر"

# التحقق من وجود Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker غير مثبت. يرجى تثبيت Docker أولاً."
    echo "Ubuntu/Debian: sudo apt install docker.io docker-compose"
    echo "CentOS/RHEL: sudo yum install docker docker-compose"
    echo "macOS: brew install docker docker-compose"
    exit 1
fi

echo "✅ Docker متوفر"

# التحقق من وجود docker-compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose غير مثبت. يرجى تثبيت docker-compose أولاً."
    exit 1
fi

echo "✅ docker-compose متوفر"

# تثبيت المكتبة المطلوبة
echo "📦 تثبيت المكتبة المطلوبة..."
if command -v pip3 &> /dev/null; then
    pip3 install requests > /dev/null 2>&1
elif command -v pip &> /dev/null; then
    pip install requests > /dev/null 2>&1
else
    echo "⚠️ pip غير متوفر، تأكد من تثبيت requests يدوياً"
fi

echo "✅ جاهز للتشغيل"
echo ""
echo "🚀 تشغيل مدير n8n..."
echo "💡 اضغط Ctrl+C لإيقاف n8n والخروج"
echo ""

# تشغيل المدير الرئيسي
$PYTHON_CMD main.py

echo ""
echo "👋 شكراً لاستخدام مدير n8n"

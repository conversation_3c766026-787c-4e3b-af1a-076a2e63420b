# إعداد Veo 3 للمشروع

## الطرق المجانية للوصول إلى Veo 3

### الطريقة الأولى: Google Cloud Skills Boost (مجاني)

#### خطوات الحصول على وصول مجاني:
1. اذه<PERSON> إلى الرابط: https://www.cloudskillsboost.google/focuses/86501
2. سجل الدخول بحساب Google
3. انقر على "Start Lab"
4. حل الكابتشا إذا ظهر
5. ستحصل على:
   - Username مؤقت
   - Password مؤقت  
   - Project ID
   - Region
   - مدة الوصول: 45 دقيقة

#### استخدام البيانات:
```bash
# معلومات الوصول المؤقت
Username: <EMAIL>
Password: xxxxxxxxxx
Project ID: qwiklabs-gcp-xx-xxxxxxxx
Region: us-central1
```

#### إيقاف وإعادة تشغيل المختبر:
- أوق<PERSON> المختبر بعد إنتاج كل فيديو
- أعد تشغيله عند الحاجة لفيديو جديد
- هذا يوفر الوقت المجاني

### الطريقة الثانية: Google AI Studio (محدود)
1. اذهب إلى: https://aistudio.google.com/
2. سجل الدخول
3. ابحث عن Veo في القائمة
4. استخدم النسخة التجريبية

### الطريقة الثالثة: Vertex AI (تجريبي)
1. اذهب إلى: https://console.cloud.google.com/vertex-ai
2. فعل Vertex AI API
3. استخدم النسخة التجريبية من Veo

## إعداد الاتصال في N8N

### استخدام HTTP Request Node:

#### للطريقة الأولى (Skills Boost):
```json
{
  "method": "POST",
  "url": "https://us-central1-aiplatform.googleapis.com/v1/projects/{{PROJECT_ID}}/locations/us-central1/publishers/google/models/veo-3:generateVideo",
  "headers": {
    "Authorization": "Bearer {{ACCESS_TOKEN}}",
    "Content-Type": "application/json"
  },
  "body": {
    "instances": [{
      "prompt": "{{VIDEO_PROMPT}}",
      "parameters": {
        "duration": "15s",
        "aspect_ratio": "9:16",
        "quality": "high"
      }
    }]
  }
}
```

#### للحصول على Access Token:
```bash
gcloud auth application-default print-access-token
```

## نماذج Prompts لـ Veo 3

### Prompt أساسي للتقطيع:
```
A professional chef's hand holding a sharp kitchen knife, positioned above a wooden cutting board. On the cutting board sits a [ITEM]. The scene is well-lit with dramatic lighting. The chef slowly brings the knife down and cuts the [ITEM] perfectly in half, revealing the inside. The cut is clean and precise. Shot in 4K, cinematic style, slow motion effect.
```

### Prompt متقدم:
```
Close-up shot of a chef's hand gripping a gleaming stainless steel knife. The camera focuses on a [ITEM] placed on a rustic wooden cutting board. Soft, warm lighting creates dramatic shadows. The knife descends in slow motion, slicing through the [ITEM] with surgical precision. The camera captures the exact moment of the cut, showing the interior texture. Professional food photography style, 4K resolution, 60fps for smooth slow motion.
```

## معالجة الاستجابة

### استجابة Veo 3 النموذجية:
```json
{
  "predictions": [{
    "video_uri": "gs://bucket-name/video-id.mp4",
    "video_id": "unique-video-id",
    "status": "completed",
    "duration": 15.0
  }]
}
```

### تحويل GCS URI إلى رابط قابل للتحميل:
```javascript
function convertGcsToDownloadUrl(gcsUri, projectId, accessToken) {
  const bucketName = gcsUri.split('/')[2];
  const fileName = gcsUri.split('/').slice(3).join('/');
  
  return `https://storage.googleapis.com/download/storage/v1/b/${bucketName}/o/${encodeURIComponent(fileName)}?alt=media&token=${accessToken}`;
}
```

## إدارة الحسابات المتعددة

### سكريبت Python لإدارة المختبرات:
```python
import time
import requests
from selenium import webdriver

class Veo3Manager:
    def __init__(self):
        self.current_lab = None
        self.labs_queue = []
    
    def start_new_lab(self):
        # كود لبدء مختبر جديد
        pass
    
    def stop_current_lab(self):
        # كود لإيقاف المختبر الحالي
        pass
    
    def generate_video(self, prompt):
        # كود لإنتاج الفيديو
        pass
```

## استكشاف الأخطاء

### خطأ 403 - Forbidden:
- تأكد من صحة Project ID
- تأكد من تفعيل Vertex AI API

### خطأ 401 - Unauthorized:
- أعد إنشاء Access Token
- تأكد من صحة بيانات المختبر

### خطأ 429 - Rate Limit:
- انتظر أو استخدم مختبر جديد
- قلل عدد الطلبات

### فشل إنتاج الفيديو:
- تأكد من وضوح الـ prompt
- تجنب المحتوى المحظور
- استخدم prompts باللغة الإنجليزية

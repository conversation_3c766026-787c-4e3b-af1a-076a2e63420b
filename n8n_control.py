#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import sys
import time
import requests
import os

class N8nController:
    def __init__(self):
        self.docker_compose_path = "."
        self.n8n_url = "http://localhost:5678"
        
    def run_command(self, command):
        """تنفيذ أمر في terminal"""
        try:
            print(f"🔄 تنفيذ: {command}")
            result = subprocess.run(
                command, 
                shell=True, 
                cwd=self.docker_compose_path,
                capture_output=True, 
                text=True, 
                timeout=60
            )
            
            if result.returncode == 0:
                print("✅ تم بنجاح")
                return True
            else:
                print(f"❌ فشل: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ انتهت مهلة تنفيذ الأمر")
            return False
        except Exception as e:
            print(f"❌ خطأ: {e}")
            return False
    
    def check_status(self):
        """التحقق من حالة n8n"""
        try:
            # التحقق من Docker container
            result = subprocess.run(
                "docker ps --filter name=n8n --format 'table {{.Names}}\t{{.Status}}'",
                shell=True,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0 and "n8n" in result.stdout and "Up" in result.stdout:
                # التحقق من استجابة n8n
                try:
                    response = requests.get(self.n8n_url, timeout=3)
                    if response.status_code == 200:
                        return "running"
                    else:
                        return "starting"
                except:
                    return "starting"
            else:
                return "stopped"
                
        except Exception as e:
            print(f"❌ خطأ في التحقق من الحالة: {e}")
            return "error"
    
    def start(self):
        """تشغيل n8n"""
        status = self.check_status()
        if status == "running":
            print("✅ n8n يعمل بالفعل")
            return True
            
        print("🚀 بدء تشغيل n8n...")
        success = self.run_command("docker-compose up -d")
        
        if success:
            print("⏳ انتظار بدء التشغيل...")
            for i in range(30):  # انتظار 30 ثانية كحد أقصى
                time.sleep(2)
                status = self.check_status()
                if status == "running":
                    print("✅ n8n يعمل الآن!")
                    print(f"🌐 افتح المتصفح على: {self.n8n_url}")
                    return True
                elif status == "starting":
                    print(f"🔄 لا يزال يبدأ... ({i*2}/60 ثانية)")
                else:
                    break
            
            print("⚠️ n8n بدأ ولكن قد يحتاج وقت إضافي")
            return True
        else:
            return False
    
    def stop(self):
        """إيقاف n8n"""
        status = self.check_status()
        if status == "stopped":
            print("⏹️ n8n متوقف بالفعل")
            return True
            
        print("⏹️ إيقاف n8n...")
        success = self.run_command("docker-compose down")
        
        if success:
            print("✅ تم إيقاف n8n (البيانات محفوظة)")
            return True
        else:
            return False
    
    def restart(self):
        """إعادة تشغيل n8n"""
        print("🔄 إعادة تشغيل n8n...")
        if self.stop():
            time.sleep(3)
            return self.start()
        return False
    
    def status(self):
        """عرض حالة n8n"""
        status = self.check_status()
        
        if status == "running":
            print("✅ n8n يعمل بشكل طبيعي")
            print(f"🌐 الرابط: {self.n8n_url}")
        elif status == "starting":
            print("🔄 n8n يبدأ التشغيل...")
        elif status == "stopped":
            print("⏹️ n8n متوقف")
        else:
            print("❌ خطأ في التحقق من الحالة")
            
        return status
    
    def logs(self):
        """عرض سجلات n8n"""
        print("📋 سجلات n8n:")
        print("=" * 50)
        self.run_command("docker-compose logs --tail 20 n8n")

def show_menu():
    """عرض القائمة"""
    print("\n" + "=" * 50)
    print("🎛️  مدير n8n")
    print("=" * 50)
    print("1. ▶️  تشغيل n8n")
    print("2. ⏹️  إيقاف n8n")
    print("3. 🔄 إعادة تشغيل n8n")
    print("4. 📊 عرض الحالة")
    print("5. 📋 عرض السجلات")
    print("6. 🌐 فتح n8n في المتصفح")
    print("7. ❌ خروج")
    print("=" * 50)

def open_browser(url):
    """فتح المتصفح"""
    import webbrowser
    try:
        webbrowser.open(url)
        print(f"🌐 تم فتح المتصفح على: {url}")
    except Exception as e:
        print(f"❌ فشل في فتح المتصفح: {e}")
        print(f"افتح المتصفح يدوياً على: {url}")

def main():
    controller = N8nController()
    
    print("🚀 مرحباً بك في مدير n8n")
    print("💾 جميع البيانات محفوظة تلقائياً")
    
    while True:
        show_menu()
        
        try:
            choice = input("\nاختر رقم العملية: ").strip()
            
            if choice == "1":
                controller.start()
            elif choice == "2":
                controller.stop()
            elif choice == "3":
                controller.restart()
            elif choice == "4":
                controller.status()
            elif choice == "5":
                controller.logs()
            elif choice == "6":
                status = controller.check_status()
                if status == "running":
                    open_browser(controller.n8n_url)
                else:
                    print("❌ n8n غير يعمل. شغله أولاً.")
            elif choice == "7":
                print("👋 شكراً لاستخدام مدير n8n")
                break
            else:
                print("❌ اختيار غير صحيح")
                
        except KeyboardInterrupt:
            print("\n👋 شكراً لاستخدام مدير n8n")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")
        
        input("\nاضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()

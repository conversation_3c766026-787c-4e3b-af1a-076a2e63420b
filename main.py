#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف التشغيل الرئيسي لـ n8n
يقوم بتشغيل n8n وإيقافه بشكل طبيعي
"""

import subprocess
import sys
import time
import signal
import os
import requests
import threading
import webbrowser
from datetime import datetime

class N8nMainManager:
    def __init__(self):
        self.n8n_process = None
        self.is_running = False
        self.should_stop = False
        self.use_docker = True
        self.n8n_url = "http://localhost:5678"
        
    def log(self, message):
        """طباعة رسالة مع الوقت"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def check_docker(self):
        """التحقق من وجود Docker"""
        try:
            # محاولة تشغيل docker --version مع timeout أقصر
            result = subprocess.run(["docker", "--version"],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                self.log("✅ Docker متوفر")

                # التحقق من أن Docker daemon يعمل
                try:
                    daemon_result = subprocess.run(["docker", "info"],
                                                 capture_output=True, text=True, timeout=5)
                    if daemon_result.returncode == 0:
                        self.log("✅ Docker daemon يعمل")
                        return True
                    else:
                        self.log("⚠️ Docker مثبت لكن daemon لا يعمل")
                        self.log("💡 تأكد من تشغيل Docker Desktop")
                        return False
                except subprocess.TimeoutExpired:
                    self.log("⚠️ Docker daemon لا يستجيب")
                    self.log("💡 تأكد من تشغيل Docker Desktop")
                    return False
            else:
                self.log("❌ Docker غير متوفر")
                self.log("💡 يرجى تثبيت Docker Desktop من: https://www.docker.com/products/docker-desktop")
                return False
        except subprocess.TimeoutExpired:
            self.log("❌ Docker لا يستجيب (timeout)")
            self.log("💡 تأكد من تشغيل Docker Desktop")
            return False
        except FileNotFoundError:
            self.log("❌ Docker غير مثبت")
            self.log("💡 يرجى تثبيت Docker Desktop من: https://www.docker.com/products/docker-desktop")
            return False
        except Exception as e:
            self.log(f"❌ خطأ في التحقق من Docker: {e}")
            return False
    
    def check_docker_compose(self):
        """التحقق من وجود docker-compose.yml"""
        if os.path.exists("docker-compose.yml"):
            self.log("✅ ملف docker-compose.yml موجود")
            return True
        else:
            self.log("❌ ملف docker-compose.yml غير موجود")
            return False

    def check_local_n8n(self):
        """التحقق من وجود n8n مثبت محلياً"""
        try:
            result = subprocess.run(["n8n", "--version"],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                self.log("✅ n8n مثبت محلياً")
                return True
            else:
                return False
        except:
            return False
    
    def start_n8n_docker(self):
        """تشغيل n8n باستخدام Docker Compose"""
        try:
            self.log("🚀 بدء تشغيل n8n باستخدام Docker...")

            # إيقاف أي حاويات سابقة
            subprocess.run(["docker-compose", "down"],
                         capture_output=True, timeout=30)

            # تشغيل n8n
            result = subprocess.run(["docker-compose", "up", "-d"],
                                  capture_output=True, text=True, timeout=60)

            if result.returncode == 0:
                self.log("✅ تم بدء تشغيل n8n")
                self.is_running = True
                return True
            else:
                self.log(f"❌ فشل في تشغيل n8n: {result.stderr}")
                return False

        except Exception as e:
            self.log(f"❌ خطأ في تشغيل n8n: {e}")
            return False

    def start_n8n_local(self):
        """تشغيل n8n محلياً"""
        try:
            self.log("🚀 بدء تشغيل n8n محلياً...")

            # تشغيل n8n في الخلفية
            self.n8n_process = subprocess.Popen(
                ["n8n", "start"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            self.log("✅ تم بدء تشغيل n8n محلياً")
            self.is_running = True
            self.use_docker = False
            return True

        except Exception as e:
            self.log(f"❌ خطأ في تشغيل n8n محلياً: {e}")
            return False

    def start_n8n(self):
        """تشغيل n8n (Docker أو محلي)"""
        if self.use_docker:
            return self.start_n8n_docker()
        else:
            return self.start_n8n_local()
    
    def stop_n8n(self):
        """إيقاف n8n"""
        try:
            self.log("⏹️ إيقاف n8n...")

            if self.use_docker:
                # إيقاف Docker
                result = subprocess.run(["docker-compose", "down"],
                                      capture_output=True, text=True, timeout=30)

                if result.returncode == 0:
                    self.log("✅ تم إيقاف n8n (البيانات محفوظة)")
                    self.is_running = False
                    return True
                else:
                    self.log(f"❌ فشل في إيقاف n8n: {result.stderr}")
                    return False
            else:
                # إيقاف العملية المحلية
                if self.n8n_process and self.n8n_process.poll() is None:
                    self.n8n_process.terminate()
                    try:
                        self.n8n_process.wait(timeout=10)
                    except subprocess.TimeoutExpired:
                        self.n8n_process.kill()

                    self.log("✅ تم إيقاف n8n (البيانات محفوظة)")
                    self.is_running = False
                    return True
                else:
                    self.log("⚠️ n8n غير يعمل")
                    self.is_running = False
                    return True

        except Exception as e:
            self.log(f"❌ خطأ في إيقاف n8n: {e}")
            return False
    
    def check_n8n_status(self):
        """التحقق من حالة n8n"""
        try:
            if self.use_docker:
                # التحقق من حالة الحاوية
                result = subprocess.run(["docker", "ps", "--filter", "name=n8n",
                                       "--format", "{{.Names}}\t{{.Status}}"],
                                      capture_output=True, text=True, timeout=5)

                if result.returncode == 0 and "n8n" in result.stdout and "Up" in result.stdout:
                    # التحقق من استجابة n8n
                    try:
                        response = requests.get(self.n8n_url, timeout=3)
                        if response.status_code == 200:
                            return "running"
                        else:
                            return "starting"
                    except requests.exceptions.RequestException:
                        return "starting"
                else:
                    return "stopped"
            else:
                # التحقق من العملية المحلية
                if self.n8n_process and self.n8n_process.poll() is None:
                    # العملية تعمل، التحقق من استجابة n8n
                    try:
                        response = requests.get(self.n8n_url, timeout=3)
                        if response.status_code == 200:
                            return "running"
                        else:
                            return "starting"
                    except requests.exceptions.RequestException:
                        return "starting"
                else:
                    return "stopped"

        except Exception:
            return "unknown"
    
    def open_browser(self):
        """فتح المتصفح على رابط n8n"""
        try:
            self.log("🌐 فتح المتصفح...")
            webbrowser.open(self.n8n_url)
            self.log("✅ تم فتح المتصفح")
        except Exception as e:
            self.log(f"⚠️ فشل في فتح المتصفح تلقائياً: {e}")
            self.log(f"💡 افتح المتصفح يدوياً على: {self.n8n_url}")

    def wait_for_n8n(self, max_wait=60):
        """انتظار حتى يصبح n8n جاهز"""
        self.log("⏳ انتظار n8n ليصبح جاهز...")

        for i in range(max_wait):
            status = self.check_n8n_status()

            if status == "running":
                self.log("✅ n8n جاهز للاستخدام!")
                self.log(f"🌐 الرابط: {self.n8n_url}")
                # فتح المتصفح تلقائياً
                threading.Timer(2.0, self.open_browser).start()
                return True
            elif status == "starting":
                if i % 5 == 0:  # طباعة كل 5 ثوان
                    self.log(f"🔄 لا يزال يبدأ... ({i}/{max_wait} ثانية)")
            elif status == "stopped":
                self.log("❌ n8n توقف أثناء البدء")
                return False

            if self.should_stop:
                return False

            time.sleep(1)

        self.log("⚠️ انتهت مهلة الانتظار، لكن n8n قد يكون لا يزال يبدأ")
        self.log(f"💡 افتح المتصفح يدوياً على: {self.n8n_url}")
        return False
    
    def monitor_n8n(self):
        """مراقبة حالة n8n"""
        while not self.should_stop and self.is_running:
            status = self.check_n8n_status()
            
            if status == "stopped" and self.is_running:
                self.log("⚠️ n8n توقف بشكل غير متوقع")
                self.is_running = False
                break
            
            time.sleep(10)  # فحص كل 10 ثوان
    
    def signal_handler(self, signum, frame):
        """معالج إشارة الإيقاف"""
        # تجاهل المتغيرات غير المستخدمة
        _ = signum, frame

        self.log("\n🛑 تم استلام إشارة الإيقاف...")
        self.should_stop = True

        if self.is_running:
            self.stop_n8n()

        self.log("👋 تم إنهاء البرنامج")
        sys.exit(0)
    
    def run(self):
        """تشغيل المدير الرئيسي"""
        # تسجيل معالج الإشارات
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        self.log("🎛️ مدير n8n الرئيسي")
        self.log("=" * 40)

        # التحقق من المتطلبات وتحديد طريقة التشغيل
        docker_available = self.check_docker()
        docker_compose_available = self.check_docker_compose()
        local_n8n_available = self.check_local_n8n()

        if docker_available and docker_compose_available:
            self.log("✅ سيتم استخدام Docker")
            self.use_docker = True
        elif local_n8n_available:
            self.log("✅ سيتم استخدام n8n المحلي")
            self.use_docker = False
        else:
            self.log("❌ لا يمكن تشغيل n8n:")
            if not docker_available:
                self.log("  • Docker غير متوفر أو لا يعمل")
            if not docker_compose_available:
                self.log("  • ملف docker-compose.yml غير موجود")
            if not local_n8n_available:
                self.log("  • n8n غير مثبت محلياً")
            self.log("")
            self.log("💡 الحلول المقترحة:")
            self.log("  1. تثبيت وتشغيل Docker Desktop")
            self.log("  2. أو تثبيت n8n محلياً: npm install -g n8n")
            return False

        # تشغيل n8n
        if not self.start_n8n():
            return False

        # انتظار n8n ليصبح جاهز
        if not self.wait_for_n8n():
            self.log("⚠️ n8n قد يحتاج وقت إضافي للبدء")

        # بدء مراقبة n8n في خيط منفصل
        monitor_thread = threading.Thread(target=self.monitor_n8n, daemon=True)
        monitor_thread.start()

        self.log("✅ n8n يعمل الآن")
        self.log("💡 اضغط Ctrl+C لإيقاف n8n والخروج")
        self.log(f"🌐 الرابط: {self.n8n_url}")

        try:
            # انتظار إشارة الإيقاف
            while not self.should_stop:
                time.sleep(1)
        except KeyboardInterrupt:
            self.signal_handler(signal.SIGINT, None)

        return True

def main():
    """الدالة الرئيسية"""
    manager = N8nMainManager()
    
    try:
        manager.run()
    except Exception as e:
        manager.log(f"❌ خطأ غير متوقع: {e}")
        if manager.is_running:
            manager.stop_n8n()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

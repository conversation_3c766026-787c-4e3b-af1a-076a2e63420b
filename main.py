#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف التشغيل الرئيسي لـ n8n
يقوم بتشغيل n8n وإيقافه بشكل طبيعي
"""

import subprocess
import sys
import time
import signal
import os
import requests
import threading
from datetime import datetime

class N8nMainManager:
    def __init__(self):
        self.n8n_process = None
        self.is_running = False
        self.should_stop = False
        
    def log(self, message):
        """طباعة رسالة مع الوقت"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def check_docker(self):
        """التحقق من وجود Docker"""
        try:
            result = subprocess.run(["docker", "--version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.log("✅ Docker متوفر")
                return True
            else:
                self.log("❌ Docker غير متوفر")
                return False
        except Exception as e:
            self.log(f"❌ خطأ في التحقق من Docker: {e}")
            return False
    
    def check_docker_compose(self):
        """التحقق من وجود docker-compose.yml"""
        if os.path.exists("docker-compose.yml"):
            self.log("✅ ملف docker-compose.yml موجود")
            return True
        else:
            self.log("❌ ملف docker-compose.yml غير موجود")
            return False
    
    def start_n8n(self):
        """تشغيل n8n باستخدام Docker Compose"""
        try:
            self.log("🚀 بدء تشغيل n8n...")
            
            # إيقاف أي حاويات سابقة
            subprocess.run(["docker-compose", "down"], 
                         capture_output=True, timeout=30)
            
            # تشغيل n8n
            result = subprocess.run(["docker-compose", "up", "-d"], 
                                  capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                self.log("✅ تم بدء تشغيل n8n")
                self.is_running = True
                return True
            else:
                self.log(f"❌ فشل في تشغيل n8n: {result.stderr}")
                return False
                
        except Exception as e:
            self.log(f"❌ خطأ في تشغيل n8n: {e}")
            return False
    
    def stop_n8n(self):
        """إيقاف n8n"""
        try:
            self.log("⏹️ إيقاف n8n...")
            
            result = subprocess.run(["docker-compose", "down"], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                self.log("✅ تم إيقاف n8n (البيانات محفوظة)")
                self.is_running = False
                return True
            else:
                self.log(f"❌ فشل في إيقاف n8n: {result.stderr}")
                return False
                
        except Exception as e:
            self.log(f"❌ خطأ في إيقاف n8n: {e}")
            return False
    
    def check_n8n_status(self):
        """التحقق من حالة n8n"""
        try:
            # التحقق من حالة الحاوية
            result = subprocess.run(["docker", "ps", "--filter", "name=n8n", 
                                   "--format", "{{.Names}}\t{{.Status}}"], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and "n8n" in result.stdout and "Up" in result.stdout:
                # التحقق من استجابة n8n
                try:
                    response = requests.get("http://localhost:5678", timeout=5)
                    if response.status_code == 200:
                        return "running"
                    else:
                        return "starting"
                except requests.exceptions.RequestException:
                    return "starting"
            else:
                return "stopped"
                
        except Exception:
            return "unknown"
    
    def wait_for_n8n(self, max_wait=60):
        """انتظار حتى يصبح n8n جاهز"""
        self.log("⏳ انتظار n8n ليصبح جاهز...")
        
        for i in range(max_wait):
            status = self.check_n8n_status()
            
            if status == "running":
                self.log("✅ n8n جاهز للاستخدام!")
                self.log("🌐 افتح المتصفح على: http://localhost:5678")
                return True
            elif status == "starting":
                if i % 5 == 0:  # طباعة كل 5 ثوان
                    self.log(f"🔄 لا يزال يبدأ... ({i}/{max_wait} ثانية)")
            elif status == "stopped":
                self.log("❌ n8n توقف أثناء البدء")
                return False
            
            if self.should_stop:
                return False
                
            time.sleep(1)
        
        self.log("⚠️ انتهت مهلة الانتظار، لكن n8n قد يكون لا يزال يبدأ")
        return False
    
    def monitor_n8n(self):
        """مراقبة حالة n8n"""
        while not self.should_stop and self.is_running:
            status = self.check_n8n_status()
            
            if status == "stopped" and self.is_running:
                self.log("⚠️ n8n توقف بشكل غير متوقع")
                self.is_running = False
                break
            
            time.sleep(10)  # فحص كل 10 ثوان
    
    def signal_handler(self, signum, frame):
        """معالج إشارة الإيقاف"""
        self.log("\n🛑 تم استلام إشارة الإيقاف...")
        self.should_stop = True
        
        if self.is_running:
            self.stop_n8n()
        
        self.log("👋 تم إنهاء البرنامج")
        sys.exit(0)
    
    def run(self):
        """تشغيل المدير الرئيسي"""
        # تسجيل معالج الإشارات
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        self.log("🎛️ مدير n8n الرئيسي")
        self.log("=" * 40)
        
        # التحقق من المتطلبات
        if not self.check_docker():
            self.log("❌ يرجى تثبيت Docker أولاً")
            return False
        
        if not self.check_docker_compose():
            self.log("❌ ملف docker-compose.yml مطلوب")
            return False
        
        # تشغيل n8n
        if not self.start_n8n():
            return False
        
        # انتظار n8n ليصبح جاهز
        if not self.wait_for_n8n():
            self.log("⚠️ n8n قد يحتاج وقت إضافي للبدء")
        
        # بدء مراقبة n8n في خيط منفصل
        monitor_thread = threading.Thread(target=self.monitor_n8n, daemon=True)
        monitor_thread.start()
        
        self.log("✅ n8n يعمل الآن")
        self.log("💡 اضغط Ctrl+C لإيقاف n8n والخروج")
        self.log("🌐 الرابط: http://localhost:5678")
        
        try:
            # انتظار إشارة الإيقاف
            while not self.should_stop:
                time.sleep(1)
        except KeyboardInterrupt:
            self.signal_handler(signal.SIGINT, None)
        
        return True

def main():
    """الدالة الرئيسية"""
    manager = N8nMainManager()
    
    try:
        manager.run()
    except Exception as e:
        manager.log(f"❌ خطأ غير متوقع: {e}")
        if manager.is_running:
            manager.stop_n8n()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

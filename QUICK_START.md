# 🚀 دليل البدء السريع - مدير n8n

## التشغيل السريع

### Windows:
```bash
start_manager.bat
```

### Linux/macOS:
```bash
./start_manager.sh
```

### يدوياً:
```bash
python n8n_manager.py
```

## 📱 الوصول للمدير

افتح المتصفح على: **http://localhost:8080**

## 🎛️ الأزرار والوظائف

| الزر | الوظيفة | الوصف |
|------|---------|-------|
| ▶️ تشغيل | تشغيل n8n | يبدأ تشغيل n8n في الخلفية |
| ⏹️ إيقاف | إيقاف n8n | يوقف n8n مع الحفاظ على البيانات |
| 🔄 إعادة تشغيل | إعادة تشغيل | يوقف ثم يشغل n8n مرة أخرى |
| 🌐 فتح n8n | فتح الواجهة | يفتح n8n في تبويب جديد |
| 📋 السجلات | عرض السجلات | يعرض سجلات n8n |

## 💾 حفظ البيانات

- ✅ **جميع المشاريع محفوظة تلقائياً** في مجلد `workflows/`
- ✅ **بيانات الاعتماد محفوظة** في مجلد `credentials/`
- ✅ **يمكنك إيقاف وتشغيل n8n دون فقدان أي بيانات**
- ✅ **البيانات محفوظة حتى لو أعدت تشغيل الكمبيوتر**

## 🔧 استكشاف الأخطاء

### المشكلة: "n8n غير مثبت أو متوقف"
**الحل:**
1. تأكد من تشغيل Docker Desktop
2. اضغط زر "تشغيل" في المدير

### المشكلة: "فشل في تشغيل n8n"
**الحل:**
1. تحقق من أن المنفذ 5678 غير مستخدم
2. تأكد من وجود ملف `docker-compose.yml`
3. اعرض السجلات لمعرفة السبب

### المشكلة: الزر لا يعمل
**الحل:**
1. انتظر حتى تكتمل العملية الحالية
2. حدث الصفحة (F5)
3. تحقق من السجلات

## 📊 حالات n8n

| الحالة | المعنى | الإجراء |
|--------|--------|---------|
| ✅ يعمل بشكل طبيعي | n8n جاهز للاستخدام | يمكنك فتح الواجهة |
| 🔄 يبدأ التشغيل | n8n في مرحلة البدء | انتظر قليلاً |
| ⏹️ متوقف | n8n غير يعمل | اضغط زر التشغيل |
| ❌ خطأ | مشكلة في التشغيل | اعرض السجلات |

## 🌐 الوصول لـ n8n

بعد التشغيل، افتح: **http://localhost:5678**

## ⚡ نصائح سريعة

- **للتشغيل السريع:** اضغط `start_manager.bat` (Windows) أو `./start_manager.sh` (Linux/Mac)
- **للمراقبة:** اترك المدير مفتوحاً لمراقبة حالة n8n
- **للأمان:** جميع البيانات محفوظة حتى لو أغلقت المدير
- **للتحديث:** أعد تشغيل المدير لتحديث الواجهة

## 🆘 الدعم

إذا واجهت أي مشكلة:
1. اعرض السجلات من المدير
2. تحقق من ملف `README.md` للتفاصيل الكاملة
3. تأكد من تشغيل Docker Desktop

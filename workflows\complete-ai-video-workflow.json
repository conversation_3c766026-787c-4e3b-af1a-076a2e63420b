{"name": "Complete AI Video Generator - Cutting Things Trend", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 2}]}}, "id": "schedule-trigger", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [240, 300]}, {"parameters": {"command": "python3", "arguments": "account_manager.py --health-check", "options": {"cwd": "/data"}}, "id": "system-health-check", "name": "System Health Check", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"jsCode": "// قائمة شاملة للأشياء المشهورة للتقطيع\nconst cuttingItems = [\n  {\n    name: 'كوكب المشتري',\n    englishName: 'Jupiter planet',\n    category: 'space',\n    difficulty: 'hard',\n    trending: true\n  },\n  {\n    name: 'تفاحة ذهبية',\n    englishName: 'golden apple',\n    category: 'fruit',\n    difficulty: 'easy',\n    trending: true\n  },\n  {\n    name: 'هاتف آيفون 15',\n    englishName: 'iPhone 15 smartphone',\n    category: 'technology',\n    difficulty: 'medium',\n    trending: true\n  },\n  {\n    name: 'فاكهة الشيطان من ون بيس',\n    englishName: 'Devil Fruit from One Piece anime',\n    category: 'anime',\n    difficulty: 'hard',\n    trending: true\n  },\n  {\n    name: 'برج إيفل مصغر',\n    englishName: 'miniature Eiffel Tower',\n    category: 'landmark',\n    difficulty: 'medium',\n    trending: false\n  },\n  {\n    name: 'كعكة عيد ميلاد',\n    englishName: 'birthday cake with candles',\n    category: 'food',\n    difficulty: 'easy',\n    trending: true\n  },\n  {\n    name: 'سيارة تسلا مصغرة',\n    englishName: 'miniature Tesla Cybertruck',\n    category: 'vehicle',\n    difficulty: 'hard',\n    trending: true\n  },\n  {\n    name: 'ساعة رولكس',\n    englishName: 'luxury Rolex watch',\n    category: 'luxury',\n    difficulty: 'medium',\n    trending: false\n  },\n  {\n    name: 'شوكولاتة فيريرو روشيه',\n    englishName: 'Ferrero Rocher chocolate',\n    category: 'candy',\n    difficulty: 'easy',\n    trending: true\n  },\n  {\n    name: 'كرة التنين من دراغون بول',\n    englishName: 'Dragon Ball from Dragon Ball Z anime',\n    category: 'anime',\n    difficulty: 'hard',\n    trending: true\n  },\n  {\n    name: 'بيتزا مارغريتا',\n    englishName: 'Margherita pizza slice',\n    category: 'food',\n    difficulty: 'easy',\n    trending: true\n  },\n  {\n    name: 'كوكب الأرض مصغر',\n    englishName: 'miniature Earth globe',\n    category: 'space',\n    difficulty: 'medium',\n    trending: false\n  }\n];\n\n// تفضيل العناصر الرائجة\nconst trendingItems = cuttingItems.filter(item => item.trending);\nconst itemsToChooseFrom = trendingItems.length > 0 ? trendingItems : cuttingItems;\n\n// اختيار عنصر عشوائي\nconst randomItem = itemsToChooseFrom[Math.floor(Math.random() * itemsToChooseFrom.length)];\n\n// إنشاء prompt متقدم ومفصل للفيديو\nconst videoPrompt = `Ultra-high quality cinematic shot: Professional chef's hand wearing black latex gloves grips a razor-sharp Damascus steel chef's knife with wooden handle. On a premium walnut cutting board sits a perfectly positioned ${randomItem.englishName}. Studio lighting setup with key light from 45-degree angle, fill light to reduce shadows, and rim light for dramatic edge lighting. The knife descends in extreme slow motion at 240fps, slicing through the ${randomItem.englishName} with surgical precision. Macro lens captures the exact moment of penetration, revealing intricate interior textures and details. Shot in 8K resolution with shallow depth of field. Color graded with warm, cinematic tones. Professional food photography aesthetic.`;\n\n// إنشاء prompt محسن للمحتوى\nconst contentPrompt = `أنشئ محتوى جذاب لفيديو TikTok يظهر تقطيع ${randomItem.name} بالسكين بطريقة مثيرة.\n\nالمتطلبات:\n1. العنوان: جذاب ومثير للفضول (30-50 حرف) مع إيموجي مناسب\n2. الوصف: جملة قصيرة تصف المحتوى\n3. 12 هاشتاق: 6 بالعربية و 6 بالإنجليزية\n4. التركيز على كلمات الترند: satisfying, oddlysatisfying, asmr, viral\n5. استخدام هاشتاقات خاصة بالفئة: ${randomItem.category}\n\nالتنسيق المطلوب:\nالعنوان: [العنوان مع الإيموجي]\nالوصف: [وصف قصير]\nالهاشتاقات: [الهاشتاقات مفصولة بمسافات]`;\n\nreturn {\n  selectedItem: randomItem,\n  videoPrompt: videoPrompt,\n  contentPrompt: contentPrompt,\n  timestamp: new Date().toISOString(),\n  category: randomItem.category,\n  difficulty: randomItem.difficulty,\n  trending: randomItem.trending\n};"}, "id": "advanced-content-generator", "name": "Advanced Content Generator", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "googleGenerativeAiApi", "resource": "text", "operation": "generate", "prompt": "={{ $json.contentPrompt }}", "options": {"model": "gemini-1.5-pro", "temperature": 0.8, "maxTokens": 400, "topP": 0.9}}, "id": "gemini-content-creator", "name": "Gemini Content Creator", "type": "n8n-nodes-base.googleGenerativeAi", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"jsCode": "// معالجة متقدمة للمحتوى المولد من Gemini\nconst generatedText = $input.first().json.text || '';\nconst lines = generatedText.split('\\n').filter(line => line.trim());\n\nlet title = '';\nlet description = '';\nlet hashtags = [];\n\n// استخراج المحتوى بطريقة ذكية\nfor (const line of lines) {\n  const trimmedLine = line.trim();\n  \n  if (trimmedLine.includes('العنوان:') || trimmedLine.includes('Title:')) {\n    title = trimmedLine.split(':')[1]?.trim() || '';\n  } else if (trimmedLine.includes('الوصف:') || trimmedLine.includes('Description:')) {\n    description = trimmedLine.split(':')[1]?.trim() || '';\n  } else if (trimmedLine.includes('الهاشتاقات:') || trimmedLine.includes('Hashtags:')) {\n    const hashtagsText = trimmedLine.split(':')[1]?.trim() || '';\n    hashtags = hashtagsText.split(/\\s+/).filter(tag => tag.startsWith('#'));\n  } else if (trimmedLine.startsWith('#')) {\n    hashtags.push(trimmedLine);\n  } else if (!title && !trimmedLine.includes(':') && trimmedLine.length > 10) {\n    title = trimmedLine;\n  }\n}\n\n// تنظيف وتحسين العنوان\ntitle = title.replace(/[\"']/g, '').trim();\nif (title && !title.match(/[🔪✨🎯🔥💯⚡]/)) {\n  title += ' 🔪✨';\n}\n\n// إنشاء هاشتاقات افتراضية محسنة\nconst previousData = $('Advanced Content Generator').first().json;\nif (hashtags.length < 8) {\n  const baseHashtags = [\n    '#تقطيع', '#مقاطع_قصيرة', '#ترند', '#مهارات', '#مثير', '#اكسبلور',\n    '#cutting', '#satisfying', '#oddlysatisfying', '#asmr', '#viral', '#fyp'\n  ];\n  \n  const categoryHashtags = {\n    'food': ['#طبخ', '#طعام', '#food', '#cooking', '#chef', '#foodie'],\n    'technology': ['#تكنولوجيا', '#تقنية', '#tech', '#gadgets', '#innovation', '#apple'],\n    'anime': ['#أنمي', '#انمي', '#anime', '#manga', '#otaku', '#onepiece'],\n    'luxury': ['#فخامة', '#رفاهية', '#luxury', '#premium', '#expensive', '#rolex'],\n    'space': ['#فضاء', '#كواكب', '#space', '#planets', '#astronomy', '#universe'],\n    'vehicle': ['#سيارات', '#تسلا', '#cars', '#tesla', '#automotive', '#electric'],\n    'candy': ['#حلويات', '#شوكولاتة', '#candy', '#chocolate', '#sweet', '#dessert']\n  };\n  \n  const categoryTags = categoryHashtags[previousData.category] || [];\n  const allTags = [...new Set([...hashtags, ...baseHashtags, ...categoryTags])];\n  hashtags = allTags.slice(0, 15);\n}\n\n// إنشاء وصف افتراضي إذا لم يوجد\nif (!description) {\n  description = `شاهد كيف نقطع ${previousData.selectedItem.name} بطريقة مثيرة ومذهلة!`;\n}\n\nreturn {\n  ...previousData,\n  title: title || `تقطيع ${previousData.selectedItem.name} بطريقة مثيرة! 🔪✨`,\n  description: description,\n  hashtags: hashtags.join(' '),\n  hashtagsArray: hashtags,\n  contentReady: true,\n  contentQuality: hashtags.length >= 10 ? 'high' : 'medium'\n};"}, "id": "advanced-content-processor", "name": "Advanced Content Processor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"command": "python3", "arguments": "veo3_manager.py", "options": {"cwd": "/data", "env": {"ACTION": "generate_video", "VIDEO_PROMPT": "={{ $json.videoPrompt }}", "ITEM_NAME": "={{ $json.selectedItem.name }}", "CATEGORY": "={{ $json.category }}"}}}, "id": "veo3-video-generator", "name": "Veo3 Video Generator", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"jsCode": "// معالجة شاملة لنتيجة إنتاج الفيديو\nconst veoResult = $input.first().json;\nconst contentData = $('Advanced Content Processor').first().json;\n\n// تحليل نتيجة Veo 3\nlet videoGenerated = false;\nlet videoUrl = '';\nlet videoId = '';\nlet errorMessage = '';\nlet processingTime = 0;\n\ntry {\n  if (veoResult.stdout) {\n    const result = JSON.parse(veoResult.stdout);\n    videoGenerated = result.success || false;\n    videoUrl = result.video_url || result.data?.video_url || '';\n    videoId = result.video_id || result.data?.video_id || '';\n    errorMessage = result.error || '';\n    processingTime = result.processing_time || 0;\n  } else if (veoResult.stderr) {\n    errorMessage = veoResult.stderr;\n  }\n} catch (e) {\n  errorMessage = `فشل في تحليل نتيجة Veo 3: ${e.message}`;\n}\n\n// إنشاء نص صوتي ديناميكي حسب الفئة\nconst audioTexts = {\n  'food': `شاهد كيف نقطع ${contentData.selectedItem.name} بطريقة احترافية! النتيجة ستذهلك`,\n  'technology': `ماذا يحدث عندما نقطع ${contentData.selectedItem.name}؟ اكتشف السر!`,\n  'anime': `من عالم الأنمي إلى الواقع! تقطيع ${contentData.selectedItem.name} بطريقة مذهلة`,\n  'luxury': `تقطيع ${contentData.selectedItem.name} الفاخر بدقة عالية ونتيجة مثيرة`,\n  'space': `رحلة عبر الفضاء! شاهد تقطيع ${contentData.selectedItem.name} بطريقة علمية`,\n  'default': `شاهد كيف نقطع ${contentData.selectedItem.name} بطريقة مثيرة ومذهلة!`\n};\n\nconst audioText = audioTexts[contentData.category] || audioTexts.default;\n\n// تقييم جودة النتيجة\nconst qualityScore = {\n  video: videoGenerated ? 100 : 0,\n  content: contentData.contentQuality === 'high' ? 100 : 75,\n  overall: 0\n};\nqualityScore.overall = (qualityScore.video + qualityScore.content) / 2;\n\nreturn {\n  ...contentData,\n  video: {\n    generated: videoGenerated,\n    url: videoUrl,\n    id: videoId,\n    processingTime: processingTime\n  },\n  audio: {\n    text: audioText,\n    ready: videoGenerated\n  },\n  error: {\n    hasError: !!errorMessage,\n    message: errorMessage\n  },\n  quality: qualityScore,\n  readyForNextStep: videoGenerated && !errorMessage\n};"}, "id": "video-result-processor", "name": "Video Result Processor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 300]}, {"parameters": {"method": "POST", "url": "https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "xi-api-key", "value": "{{ $credentials.elevenlabsApiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{ $json.audio.text }}"}, {"name": "voice_settings", "value": "{\"stability\": 0.6, \"similarity_boost\": 0.8, \"style\": 0.4, \"use_speaker_boost\": true}"}, {"name": "model_id", "value": "eleven_multilingual_v2"}, {"name": "output_format", "value": "mp3_44100_128"}]}, "options": {"response": {"response": {"responseFormat": "file"}}}}, "id": "enhanced-audio-generator", "name": "Enhanced Audio Generator", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1780, 300]}, {"parameters": {"command": "python3", "arguments": "audio_video_merger.py", "options": {"cwd": "/data", "env": {"VIDEO_URL": "={{ $('Video Result Processor').first().json.video.url }}", "AUDIO_FILE": "audio_{{ $('Video Result Processor').first().json.selectedItem.name.replace(' ', '_') }}.mp3", "OUTPUT_NAME": "final_{{ $('Video Result Processor').first().json.selectedItem.name.replace(' ', '_') }}_{{ new Date().getTime() }}.mp4", "QUALITY": "high"}}}, "id": "video-audio-merger", "name": "Video Audio Merger", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2000, 300]}, {"parameters": {"command": "python3", "arguments": "account_manager.py", "options": {"cwd": "/data", "env": {"ACTION": "upload_tiktok", "VIDEO_PATH": "={{ $json.stdout }}", "TITLE": "={{ $('Video Result Processor').first().json.title }}", "DESCRIPTION": "={{ $('Video Result Processor').first().json.description }}", "HASHTAGS": "={{ $('Video Result Processor').first().json.hashtags }}", "CATEGORY": "={{ $('Video Result Processor').first().json.category }}"}}}, "id": "smart-tiktok-uploader", "name": "Smart TikTok Uploader", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2220, 300]}, {"parameters": {"jsCode": "// تقرير نهائي شامل ومفصل\nconst uploadResult = $input.first().json;\nconst videoData = $('Video Result Processor').first().json;\nconst mergerResult = $('Video Audio Merger').first().json;\nconst audioResult = $('Enhanced Audio Generator').first().json;\n\n// تحليل نتائج الرفع\nlet uploadSuccess = false;\nlet tiktokUrl = '';\nlet uploadError = '';\nlet accountUsed = '';\n\ntry {\n  if (uploadResult.stdout) {\n    const result = JSON.parse(uploadResult.stdout);\n    uploadSuccess = result.success || false;\n    tiktokUrl = result.tiktok_url || result.share_url || '';\n    uploadError = result.error || '';\n    accountUsed = result.account_used || 'unknown';\n  }\n} catch (e) {\n  uploadError = `فشل في تحليل نتيجة الرفع: ${e.message}`;\n}\n\n// حساب الوقت الإجمالي\nconst startTime = new Date(videoData.timestamp);\nconst endTime = new Date();\nconst totalDuration = Math.round((endTime - startTime) / 1000); // بالثواني\n\n// تقييم الجودة الإجمالية\nconst overallQuality = {\n  video: videoData.video.generated ? 'excellent' : 'failed',\n  audio: audioResult.data ? 'excellent' : 'failed',\n  content: videoData.quality.content >= 90 ? 'excellent' : videoData.quality.content >= 75 ? 'good' : 'average',\n  upload: uploadSuccess ? 'excellent' : 'failed'\n};\n\n// إنشاء التقرير الشامل\nconst comprehensiveReport = {\n  // معلومات المشروع\n  project: {\n    id: `video_${Date.now()}`,\n    timestamp: endTime.toISOString(),\n    duration: `${Math.floor(totalDuration / 60)}:${(totalDuration % 60).toString().padStart(2, '0')}`,\n    status: uploadSuccess ? 'completed' : 'failed'\n  },\n  \n  // تفاصيل المحتوى\n  content: {\n    item: videoData.selectedItem,\n    title: videoData.title,\n    description: videoData.description,\n    hashtags: videoData.hashtagsArray,\n    category: videoData.category,\n    trending: videoData.trending,\n    difficulty: videoData.difficulty\n  },\n  \n  // نتائج الإنتاج\n  production: {\n    video: {\n      generated: videoData.video.generated,\n      url: videoData.video.url,\n      id: videoData.video.id,\n      processingTime: videoData.video.processingTime,\n      quality: overallQuality.video\n    },\n    audio: {\n      generated: !!audioResult.data,\n      text: videoData.audio.text,\n      quality: overallQuality.audio\n    },\n    merger: {\n      success: !!mergerResult.stdout,\n      outputPath: mergerResult.stdout || '',\n      error: mergerResult.stderr || ''\n    }\n  },\n  \n  // نتائج النشر\n  publishing: {\n    platform: 'tiktok',\n    success: uploadSuccess,\n    url: tiktokUrl,\n    accountUsed: accountUsed,\n    error: uploadError\n  },\n  \n  // تقييم الجودة\n  quality: {\n    overall: Object.values(overallQuality).filter(q => q === 'excellent').length / Object.keys(overallQuality).length * 100,\n    breakdown: overallQuality,\n    contentScore: videoData.quality.overall\n  },\n  \n  // الأخطاء والمشاكل\n  issues: {\n    hasErrors: videoData.error.hasError || !!uploadError || !!mergerResult.stderr,\n    errors: [\n      videoData.error.hasError ? `Video: ${videoData.error.message}` : null,\n      uploadError ? `Upload: ${uploadError}` : null,\n      mergerResult.stderr ? `Merger: ${mergerResult.stderr}` : null\n    ].filter(Boolean)\n  },\n  \n  // التوصيات والخطوات التالية\n  recommendations: {\n    immediate: [],\n    longTerm: [],\n    optimizations: []\n  },\n  \n  // إحصائيات الأداء\n  performance: {\n    totalTime: totalDuration,\n    videoGenerationTime: videoData.video.processingTime,\n    successRate: uploadSuccess ? 100 : 0,\n    efficiency: totalDuration < 300 ? 'high' : totalDuration < 600 ? 'medium' : 'low'\n  }\n};\n\n// إضافة التوصيات بناءً على النتائج\nif (!videoData.video.generated) {\n  comprehensiveReport.recommendations.immediate.push('إعادة المحاولة مع prompt مختلف أو حساب Cloud جديد');\n  comprehensiveReport.recommendations.longTerm.push('تحسين جودة prompts وإضافة المزيد من حسابات Cloud');\n}\n\nif (!uploadSuccess) {\n  comprehensiveReport.recommendations.immediate.push('فحص حالة حسابات TikTok وإعادة المحاولة');\n  comprehensiveReport.recommendations.longTerm.push('إضافة المزيد من حسابات TikTok وتحسين نظام التدوير');\n}\n\nif (videoData.quality.overall < 80) {\n  comprehensiveReport.recommendations.optimizations.push('تحسين جودة المحتوى المولد من Gemini');\n  comprehensiveReport.recommendations.optimizations.push('استخدام prompts أكثر تفصيلاً');\n}\n\nif (totalDuration > 600) {\n  comprehensiveReport.recommendations.optimizations.push('تحسين سرعة المعالجة وتقليل أوقات الانتظار');\n}\n\nif (uploadSuccess) {\n  comprehensiveReport.recommendations.immediate.push('مراقبة أداء المقطع على TikTok');\n  comprehensiveReport.recommendations.longTerm.push('تحليل التفاعل لتحسين المحتوى القادم');\n}\n\n// تسجيل التقرير\nconsole.log('\\n=== تقرير إنتاج المحتوى الشامل ===');\nconsole.log(`المشروع: ${comprehensiveReport.project.id}`);\nconsole.log(`الحالة: ${comprehensiveReport.project.status}`);\nconsole.log(`المدة الإجمالية: ${comprehensiveReport.project.duration}`);\nconsole.log(`جودة المحتوى: ${comprehensiveReport.quality.overall.toFixed(1)}%`);\nconsole.log(`العنصر المقطوع: ${comprehensiveReport.content.item.name}`);\nconsole.log(`رابط TikTok: ${comprehensiveReport.publishing.url || 'غير متوفر'}`);\nconsole.log('=====================================\\n');\n\nreturn comprehensiveReport;"}, "id": "comprehensive-final-reporter", "name": "Comprehensive Final Reporter", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2440, 300]}], "connections": {"Schedule Trigger": {"main": [[{"node": "System Health Check", "type": "main", "index": 0}]]}, "System Health Check": {"main": [[{"node": "Advanced Content Generator", "type": "main", "index": 0}]]}, "Advanced Content Generator": {"main": [[{"node": "Gemini Content Creator", "type": "main", "index": 0}]]}, "Gemini Content Creator": {"main": [[{"node": "Advanced Content Processor", "type": "main", "index": 0}]]}, "Advanced Content Processor": {"main": [[{"node": "Veo3 Video Generator", "type": "main", "index": 0}]]}, "Veo3 Video Generator": {"main": [[{"node": "Video Result Processor", "type": "main", "index": 0}]]}, "Video Result Processor": {"main": [[{"node": "Enhanced Audio Generator", "type": "main", "index": 0}]]}, "Enhanced Audio Generator": {"main": [[{"node": "Video Audio Merger", "type": "main", "index": 0}]]}, "Video Audio Merger": {"main": [[{"node": "Smart TikTok Uploader", "type": "main", "index": 0}]]}, "Smart TikTok Uploader": {"main": [[{"node": "Comprehensive Final Reporter", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": ["ai", "video", "veo3", "tiktok", "automation", "cutting-trend"], "triggerCount": 0, "updatedAt": "2024-12-19T14:00:00.000Z", "versionId": "3"}
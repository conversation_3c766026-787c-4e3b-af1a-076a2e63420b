# 🎬 دليل إعداد مشروع وكيل إنتاج الفيديو بالذكاء الاصطناعي

## 📋 نظرة عامة على المشروع

هذا المشروع عبارة عن وكيل ذكي متكامل يعمل على N8N لإنتاج مقاطع فيديو قصيرة تلقائياً باستخدام:
- **Veo 3** لإنتاج الفيديو
- **Gemini 2.5 Pro** لتوليد المحتوى والأفكار
- **ElevenLabs** لتوليد الصوت
- **TikTok API** للنشر التلقائي

### 🎯 الهدف
إنتاج مقاطع فيديو عن "تقطيع الأشياء" - الترند الشائع الذي يظهر يد طباخ تقطع أشياء مختلفة بالسكين.

## 🛠️ المتطلبات الأساسية

### البرامج المطلوبة:
- Docker & Docker Compose
- Python 3.8+
- FFmpeg
- Git

### الحسابات المطلوبة:
1. **Google Cloud** (للوصول لـ Veo 3)
2. **Google AI Studio** (لـ Gemini API)
3. **ElevenLabs** (للصوت)
4. **TikTok Business** (للنشر)

## 🚀 خطوات التثبيت

### 1. إعداد البيئة الأساسية

```bash
# استنساخ المشروع
git clone <repository-url>
cd n8n-ai-video-generator

# تشغيل N8N
docker-compose up -d

# تثبيت المتطلبات Python
pip install -r requirements.txt
```

### 2. إعداد N8N

1. افتح المتصفح على: http://localhost:5678
2. أنشئ حساب مستخدم جديد
3. استورد الـ workflows من مجلد `workflows/`

### 3. إعداد بيانات الاعتماد

#### أ. Gemini API:
1. اذهب إلى: https://aistudio.google.com/
2. احصل على API Key
3. في N8N: Settings > Credentials > Add > Google Generative AI API
4. أدخل API Key

#### ب. ElevenLabs:
1. سجل في: https://elevenlabs.io/
2. احصل على API Key من Dashboard
3. في N8N: Settings > Credentials > Add > HTTP Header Auth
4. Name: `xi-api-key`, Value: `YOUR_API_KEY`

#### ج. Google Cloud (لـ Veo 3):
1. اتبع الدليل في `credentials/veo3-setup.md`
2. استخدم Google Cloud Skills Boost للوصول المجاني

#### د. TikTok Business API:
1. اتبع الدليل في `credentials/tiktok-setup.md`
2. احصل على موافقة Content Posting API

## 📁 هيكل المشروع

```
n8n-ai-video-generator/
├── workflows/                 # ملفات N8N workflows
│   ├── complete-ai-video-workflow.json
│   └── enhanced-veo3-workflow.json
├── credentials/              # أدلة إعداد APIs
│   ├── gemini-api-setup.md
│   ├── veo3-setup.md
│   ├── audio-setup.md
│   └── tiktok-setup.md
├── veo3_manager.py          # مدير Veo 3
├── account_manager.py       # مدير الحسابات المتعددة
├── audio_video_merger.py    # دمج الفيديو والصوت
├── docker-compose.yml       # إعداد Docker
└── PROJECT_SETUP_GUIDE.md   # هذا الدليل
```

## ⚙️ إعداد الـ Workflows

### 1. استيراد Workflow الرئيسي:
1. في N8N، اذهب إلى Workflows
2. انقر على "Import from File"
3. اختر `workflows/complete-ai-video-workflow.json`

### 2. تكوين العقد:
- **Schedule Trigger**: اضبط التوقيت المطلوب (كل ساعتين افتراضياً)
- **Gemini Content Creator**: تأكد من ربط credentials
- **Enhanced Audio Generator**: تأكد من ربط ElevenLabs credentials
- **Smart TikTok Uploader**: تأكد من ربط TikTok credentials

## 🔧 إعداد السكريبتات

### 1. إعداد مدير Veo 3:
```bash
# إنشاء ملف إعدادات
python3 veo3_manager.py --init

# إضافة حساب Cloud جديد
python3 veo3_manager.py --add-account \
  --username "<EMAIL>" \
  --password "password" \
  --project-id "qwiklabs-gcp-xx"
```

### 2. إعداد مدير الحسابات:
```bash
# إضافة حساب TikTok
python3 account_manager.py --add-tiktok \
  --username "your_username" \
  --password "your_password"

# فحص صحة النظام
python3 account_manager.py --health-check
```

## 🎮 تشغيل المشروع

### التشغيل التلقائي:
1. فعل الـ workflow في N8N
2. سيعمل حسب الجدولة المحددة
3. راقب السجلات في N8N Dashboard

### التشغيل اليدوي:
1. في N8N، اذهب إلى الـ workflow
2. انقر على "Execute Workflow"
3. راقب تقدم العملية

### اختبار المكونات:
```bash
# اختبار Veo 3
python3 veo3_manager.py --test

# اختبار دمج الفيديو
python3 audio_video_merger.py --test

# اختبار مدير الحسابات
python3 account_manager.py --status
```

## 📊 مراقبة الأداء

### مؤشرات الأداء الرئيسية:
- معدل نجاح إنتاج الفيديو
- معدل نجاح النشر على TikTok
- الوقت الإجمالي للعملية
- جودة المحتوى المولد

### السجلات والتقارير:
- سجلات N8N: في واجهة N8N
- سجلات Python: في `/var/log/n8n_video_generator.log`
- تقارير الأداء: في مجلد `reports/`

## 🔍 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. فشل إنتاج الفيديو:
- تحقق من صحة حسابات Google Cloud
- تأكد من وضوح الـ video prompts
- جرب حساب Cloud جديد

#### 2. مشاكل في الصوت:
- تحقق من ElevenLabs API quota
- تأكد من جودة النص المدخل
- جرب نموذج صوت مختلف

#### 3. فشل النشر على TikTok:
- تحقق من حالة حسابات TikTok
- تأكد من مواصفات الفيديو
- راجع سياسات TikTok

#### 4. مشاكل في الأداء:
- راقب استخدام الذاكرة
- تحقق من سرعة الإنترنت
- قلل جودة الفيديو إذا لزم الأمر

## 🔒 الأمان والخصوصية

### نصائح الأمان:
- استخدم متغيرات البيئة للمفاتيح الحساسة
- لا تشارك API keys في الكود
- استخدم حسابات منفصلة للاختبار
- احتفظ بنسخ احتياطية من الإعدادات

### إدارة الحسابات:
- دور حسابات TikTok لتجنب الحظر
- استخدم VPN إذا لزم الأمر
- راقب حدود الاستخدام لكل API

## 📈 التحسين والتطوير

### تحسينات مقترحة:
1. **إضافة المزيد من المصادر للفيديو**:
   - Runway ML
   - Stable Video Diffusion
   - Pika Labs

2. **تحسين جودة المحتوى**:
   - استخدام نماذج أكثر تقدماً
   - تحليل الترندات الحالية
   - تخصيص المحتوى حسب الجمهور

3. **أتمتة إضافية**:
   - إنشاء حسابات Cloud تلقائياً
   - تحليل أداء المقاطع
   - تحسين الهاشتاقات بناءً على الأداء

4. **منصات إضافية**:
   - YouTube Shorts
   - Instagram Reels
   - Twitter Videos

## 🆘 الدعم والمساعدة

### الموارد المفيدة:
- [وثائق N8N](https://docs.n8n.io/)
- [وثائق Gemini API](https://ai.google.dev/docs)
- [وثائق ElevenLabs](https://docs.elevenlabs.io/)
- [وثائق TikTok Business API](https://developers.tiktok.com/)

### الحصول على المساعدة:
1. راجع ملفات الإعداد في مجلد `credentials/`
2. تحقق من السجلات للأخطاء التفصيلية
3. جرب التشغيل اليدوي لكل مكون
4. استخدم أدوات الاختبار المدمجة

## 🎉 البدء السريع

للبدء السريع، اتبع هذه الخطوات:

1. **تشغيل N8N**:
   ```bash
   docker-compose up -d
   ```

2. **استيراد الـ workflow**:
   - افتح http://localhost:5678
   - استورد `complete-ai-video-workflow.json`

3. **إعداد API واحد على الأقل**:
   - Gemini API (مجاني)
   - ElevenLabs (تجربة مجانية)

4. **تشغيل اختبار**:
   - فعل الـ workflow يدوياً
   - راقب النتائج

5. **تحسين الإعدادات**:
   - اضبط التوقيت
   - حسن جودة المحتوى
   - أضف المزيد من الحسابات

---

🎬 **مبروك! مشروعك جاهز لإنتاج مقاطع فيديو مذهلة بالذكاء الاصطناعي!** 🎬

# 🎬 وكيل إنتاج الفيديو بالذكاء الاصطناعي

مشروع متكامل لإنتاج مقاطع فيديو قصيرة تلقائياً باستخدام الذكاء الاصطناعي ونشرها على TikTok.

## 🌟 المميزات الرئيسية

- **إنتاج فيديو تلقائي** باستخدام Google Veo 3
- **توليد المحتوى الذكي** باستخدام Gemini 2.5 Pro
- **إنتاج الصوت الطبيعي** باستخدام ElevenLabs
- **النشر التلقائي** على TikTok
- **إدارة الحسابات المتعددة** للوصول المجاني
- **مراقبة الأداء** والتقارير التفصيلية

## 🎯 موضوع المحتوى

يركز المشروع على إنتاج مقاطع **"تقطيع الأشياء"** - الترند الشائع الذي يظهر:
- يد طباخ محترف تحمل سكين حاد
- تقطيع أشياء مختلفة ومثيرة (فواكه، تكنولوجيا، أشياء من الأنمي، إلخ)
- إظهار النتيجة بطريقة مثيرة ومرضية

## 🚀 البدء السريع

### 1. تشغيل N8N
```bash
docker-compose up -d
```

### 2. الوصول إلى N8N
افتح المتصفح على: http://localhost:5678

### 3. استيراد الـ Workflow
1. اذهب إلى Workflows في N8N
2. انقر على "Import from File"
3. اختر `workflows/complete-ai-video-workflow.json`

### 4. إعداد APIs
راجع الدليل الشامل في: `PROJECT_SETUP_GUIDE.md`

## 📁 هيكل المشروع

```
├── workflows/                 # ملفات N8N workflows
├── credentials/              # أدلة إعداد APIs
├── veo3_manager.py          # مدير Veo 3
├── account_manager.py       # مدير الحسابات
├── audio_video_merger.py    # دمج الفيديو والصوت
└── PROJECT_SETUP_GUIDE.md   # الدليل الشامل
```

## 🔧 المتطلبات

### البرامج:
- Docker & Docker Compose
- Python 3.8+
- FFmpeg

### الحسابات المطلوبة:
- Google Cloud (للوصول لـ Veo 3)
- Google AI Studio (لـ Gemini API)
- ElevenLabs (للصوت)
- TikTok Business (للنشر)

## 🎮 كيفية العمل

1. **جدولة تلقائية**: يعمل الوكيل كل ساعتين
2. **اختيار المحتوى**: يختار عشوائياً شيء للتقطيع
3. **توليد الأفكار**: Gemini ينشئ العنوان والهاشتاقات
4. **إنتاج الفيديو**: Veo 3 ينشئ مقطع التقطيع
5. **إضافة الصوت**: ElevenLabs ينتج التعليق الصوتي
6. **دمج المحتوى**: دمج الفيديو مع الصوت
7. **النشر التلقائي**: رفع على TikTok مع العنوان والهاشتاقات

## 📊 مراقبة الأداء

### مؤشرات الأداء:
- معدل نجاح إنتاج الفيديو
- معدل نجاح النشر
- جودة المحتوى المولد
- الوقت الإجمالي للعملية

### التقارير:
- تقارير تفصيلية لكل عملية
- إحصائيات الاستخدام
- تتبع الأخطاء والمشاكل

## 🔒 الأمان

- إدارة آمنة للـ API keys
- تدوير الحسابات لتجنب الحظر
- نسخ احتياطية تلقائية
- مراقبة حدود الاستخدام

## 🆘 الدعم

للحصول على الدليل الشامل والمساعدة:
📖 **اقرأ**: `PROJECT_SETUP_GUIDE.md`

### الملفات المهمة:
- `credentials/`: أدلة إعداد جميع APIs
- `workflows/`: ملفات N8N جاهزة للاستيراد
- `requirements.txt`: متطلبات Python

## 🎉 ابدأ الآن!

```bash
# 1. تشغيل N8N
docker-compose up -d

# 2. تثبيت المتطلبات
pip install -r requirements.txt

# 3. افتح N8N
# http://localhost:5678

# 4. استورد الـ workflow
# workflows/complete-ai-video-workflow.json
```

---
🎬 **استمتع بإنتاج مقاطع فيديو مذهلة بالذكاء الاصطناعي!** 🎬

{"name": "AI Video Generator - Cutting Things Trend", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 2}]}}, "id": "trigger-node", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [240, 300]}, {"parameters": {"jsCode": "// قائمة الأشياء المشهورة للتقطيع\nconst cuttingItems = [\n  'كوكب المشتري',\n  'تفاحة ذهبية',\n  'هاتف آيفون',\n  'كرة التنين من دراغون بول',\n  'فاكهة الشيطان من ون بيس',\n  'مبنى برج إيفل مصغر',\n  'كعكة عيد ميلاد',\n  'سيارة تسلا مصغرة',\n  'كوكب الأرض',\n  'ساعة رولكس',\n  'لابتوب ماك بوك',\n  'كرة قدم ذهبية',\n  'قلم رصاص عملاق',\n  'شوكولاتة فيريرو روشيه',\n  'كوب قهوة ستاربكس'\n];\n\n// اختيار عنصر عشوائي\nconst randomItem = cuttingItems[Math.floor(Math.random() * cuttingItems.length)];\n\n// إنشاء prompt للفيديو\nconst videoPrompt = `A professional chef's hand holding a sharp kitchen knife, positioned above a wooden cutting board. On the cutting board sits ${randomItem}. The scene is well-lit with dramatic lighting. The chef slowly brings the knife down and cuts the ${randomItem} perfectly in half, revealing the inside. The cut is clean and precise. Shot in 4K, cinematic style, slow motion effect.`;\n\n// إنشاء prompt للعنوان والهاشتاقات\nconst contentPrompt = `Create a catchy Arabic title and relevant hashtags for a TikTok video about cutting ${randomItem} in half. The video shows a chef's knife cutting through the item. Make it engaging and trending. Format: Title on first line, then hashtags on separate lines.`;\n\nreturn {\n  selectedItem: randomItem,\n  videoPrompt: videoPrompt,\n  contentPrompt: contentPrompt,\n  timestamp: new Date().toISOString()\n};"}, "id": "content-generator", "name": "Content Generator", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "googleGenerativeAiApi", "resource": "text", "operation": "generate", "prompt": "={{ $json.contentPrompt }}", "options": {"model": "gemini-2.5-pro", "temperature": 0.7, "maxTokens": 200}}, "id": "gemini-content", "name": "Gemini - Generate Content", "type": "n8n-nodes-base.googleGenerativeAi", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"jsCode": "// معالجة النص المولد من Gemini\nconst generatedText = $input.first().json.text || '';\nconst lines = generatedText.split('\\n').filter(line => line.trim());\n\nlet title = '';\nlet hashtags = [];\n\n// استخراج العنوان والهاشتاقات\nfor (let i = 0; i < lines.length; i++) {\n  const line = lines[i].trim();\n  if (i === 0 && !line.startsWith('#')) {\n    title = line;\n  } else if (line.startsWith('#')) {\n    hashtags.push(line);\n  }\n}\n\n// إضافة هاشتاقات افتراضية إذا لم توجد\nif (hashtags.length === 0) {\n  hashtags = [\n    '#تقطيع',\n    '#مقاطع_قصيرة',\n    '#ترند',\n    '#طبخ',\n    '#مهارات',\n    '#فيديو_مثير',\n    '#اكسبلور',\n    '#viral',\n    '#cutting',\n    '#satisfying'\n  ];\n}\n\n// دمج البيانات من العقدة السابقة\nconst previousData = $('Content Generator').first().json;\n\nreturn {\n  ...previousData,\n  title: title || `تقطيع ${previousData.selectedItem} بطريقة مثيرة! 🔪✨`,\n  hashtags: hashtags.join(' '),\n  hashtagsArray: hashtags\n};"}, "id": "content-processor", "name": "Content Processor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"method": "POST", "url": "https://api.example.com/veo3/generate", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpBasicAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer {{ $credentials.googleCloudToken }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "prompt", "value": "={{ $json.videoPrompt }}"}, {"name": "duration", "value": "15"}, {"name": "aspect_ratio", "value": "9:16"}, {"name": "quality", "value": "high"}]}, "options": {"timeout": 300000}}, "id": "veo3-generator", "name": "Veo 3 - Generate Video", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1120, 300]}, {"parameters": {"jsCode": "// معالجة استجابة Veo 3\nconst response = $input.first().json;\nlet videoUrl = '';\nlet videoId = '';\n\n// استخراج رابط الفيديو من الاستجابة\nif (response.video_url) {\n  videoUrl = response.video_url;\n} else if (response.data && response.data.video_url) {\n  videoUrl = response.data.video_url;\n} else if (response.result && response.result.video_url) {\n  videoUrl = response.result.video_url;\n}\n\n// استخراج معرف الفيديو\nif (response.video_id) {\n  videoId = response.video_id;\n} else if (response.id) {\n  videoId = response.id;\n}\n\n// دمج البيانات من العقد السابقة\nconst contentData = $('Content Processor').first().json;\n\nreturn {\n  ...contentData,\n  videoUrl: videoUrl,\n  videoId: videoId,\n  videoGenerated: !!videoUrl,\n  generationTime: new Date().toISOString()\n};"}, "id": "video-processor", "name": "Video Processor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 300]}, {"parameters": {"method": "POST", "url": "https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "xi-api-key", "value": "{{ $credentials.elevenlabsApiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "شاهد كيف نقطع {{ $json.selectedItem }} بطريقة مثيرة ومذهلة!"}, {"name": "voice_settings", "value": "{\"stability\": 0.5, \"similarity_boost\": 0.5}"}]}}, "id": "audio-generator", "name": "Generate Audio", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 300]}, {"parameters": {"jsCode": "// دمج الفيديو مع الصوت باستخدام FFmpeg\nconst videoData = $('Video Processor').first().json;\nconst audioResponse = $input.first().json;\n\n// هنا يمكن إضافة كود لدمج الفيديو مع الصوت\n// أو استخدام خدمة خارجية لدمج الملفات\n\nreturn {\n  ...videoData,\n  audioGenerated: true,\n  readyForUpload: true,\n  finalVideoUrl: videoData.videoUrl, // مؤقتاً حتى يتم تطبيق دمج الصوت\n  processingComplete: new Date().toISOString()\n};"}, "id": "video-audio-merger", "name": "Merge Video & Audio", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 300]}, {"parameters": {"method": "POST", "url": "https://api.tiktok.com/v1/videos/upload", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $credentials.tiktokAccessToken }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "video_url", "value": "={{ $json.finalVideoUrl }}"}, {"name": "title", "value": "={{ $json.title }}"}, {"name": "description", "value": "={{ $json.hashtags }}"}, {"name": "privacy_level", "value": "public"}]}}, "id": "tiktok-uploader", "name": "Upload to TikTok", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2000, 300]}, {"parameters": {"jsCode": "// تسجيل نتائج النشر وإرسال تقرير\nconst uploadResponse = $input.first().json;\nconst finalData = $('Merge Video & Audio').first().json;\n\nconst report = {\n  success: !!uploadResponse.video_id,\n  videoId: uploadResponse.video_id || 'failed',\n  title: finalData.title,\n  selectedItem: finalData.selectedItem,\n  uploadTime: new Date().toISOString(),\n  tiktokUrl: uploadResponse.share_url || 'N/A'\n};\n\n// يمكن إضافة إرسال تقرير عبر البريد الإلكتروني أو Webhook\nconsole.log('Video Upload Report:', report);\n\nreturn report;"}, "id": "final-reporter", "name": "Final Report", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2220, 300]}], "connections": {"Schedule Trigger": {"main": [[{"node": "Content Generator", "type": "main", "index": 0}]]}, "Content Generator": {"main": [[{"node": "Gemini - Generate Content", "type": "main", "index": 0}]]}, "Gemini - Generate Content": {"main": [[{"node": "Content Processor", "type": "main", "index": 0}]]}, "Content Processor": {"main": [[{"node": "Veo 3 - Generate Video", "type": "main", "index": 0}]]}, "Veo 3 - Generate Video": {"main": [[{"node": "Video Processor", "type": "main", "index": 0}]]}, "Video Processor": {"main": [[{"node": "Generate Audio", "type": "main", "index": 0}]]}, "Generate Audio": {"main": [[{"node": "Merge Video & Audio", "type": "main", "index": 0}]]}, "Merge Video & Audio": {"main": [[{"node": "Upload to TikTok", "type": "main", "index": 0}]]}, "Upload to TikTok": {"main": [[{"node": "Final Report", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-12-19T10:00:00.000Z", "versionId": "1"}
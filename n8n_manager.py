#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import time
import requests
import json
import os
import sys
from flask import Flask, render_template_string, jsonify, request
import threading
import signal

app = Flask(__name__)

# إعداد معالج الإشارات للإغلاق النظيف
def signal_handler(sig, frame):
    print('\n🛑 إيقاف المدير...')
    sys.exit(0)

signal.signal(signal.SIGINT, signal_handler)

class N8nManager:
    def __init__(self):
        self.docker_compose_path = "."
        self.n8n_url = "http://localhost:5678"
        
    def run_command(self, command):
        """تنفيذ أمر في terminal"""
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                cwd=self.docker_compose_path,
                capture_output=True, 
                text=True, 
                timeout=60
            )
            return {
                "success": result.returncode == 0,
                "output": result.stdout,
                "error": result.stderr,
                "returncode": result.returncode
            }
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "output": "",
                "error": "انتهت مهلة تنفيذ الأمر",
                "returncode": -1
            }
        except Exception as e:
            return {
                "success": False,
                "output": "",
                "error": str(e),
                "returncode": -1
            }
    
    def check_status(self):
        """التحقق من حالة n8n"""
        try:
            # التحقق من حالة Docker container أولاً
            result = self.run_command("docker ps --filter name=n8n --format 'table {{.Names}}\t{{.Status}}'")

            if result["success"] and "n8n" in result["output"] and "Up" in result["output"]:
                # الحاوية تعمل، التحقق من أن n8n يستجيب
                try:
                    response = requests.get(self.n8n_url, timeout=3)
                    if response.status_code == 200:
                        return {"status": "running", "message": "✅ n8n يعمل بشكل طبيعي"}
                    else:
                        return {"status": "starting", "message": "🔄 n8n يبدأ التشغيل..."}
                except requests.exceptions.RequestException:
                    return {"status": "starting", "message": "🔄 n8n يبدأ التشغيل..."}
            else:
                # التحقق من وجود الحاوية حتى لو متوقفة
                result2 = self.run_command("docker ps -a --filter name=n8n --format 'table {{.Names}}\t{{.Status}}'")
                if result2["success"] and "n8n" in result2["output"]:
                    if "Exited" in result2["output"]:
                        return {"status": "stopped", "message": "⏹️ n8n متوقف"}
                    else:
                        return {"status": "starting", "message": "🔄 n8n يبدأ التشغيل..."}
                else:
                    return {"status": "stopped", "message": "⏹️ n8n غير مثبت أو متوقف"}

        except Exception as e:
            return {"status": "error", "message": f"❌ خطأ في التحقق من الحالة: {str(e)}"}
    
    def start(self):
        """تشغيل n8n"""
        # التحقق من الحالة الحالية أولاً
        status = self.check_status()
        if status["status"] == "running":
            return {"success": False, "message": "n8n يعمل بالفعل"}

        result = self.run_command("docker-compose up -d")
        if result["success"]:
            # انتظار قليل للتأكد من بدء التشغيل
            time.sleep(2)
            return {"success": True, "message": "✅ تم بدء تشغيل n8n"}
        else:
            return {"success": False, "message": f"❌ فشل في تشغيل n8n: {result['error']}"}

    def stop(self):
        """إيقاف n8n مع الحفاظ على البيانات"""
        # التحقق من الحالة الحالية أولاً
        status = self.check_status()
        if status["status"] == "stopped":
            return {"success": False, "message": "n8n متوقف بالفعل"}

        result = self.run_command("docker-compose down")
        if result["success"]:
            # انتظار قليل للتأكد من الإيقاف
            time.sleep(2)
            return {"success": True, "message": "⏹️ تم إيقاف n8n (البيانات محفوظة)"}
        else:
            return {"success": False, "message": f"❌ فشل في إيقاف n8n: {result['error']}"}

    def restart(self):
        """إعادة تشغيل n8n"""
        # إيقاف ثم تشغيل
        stop_result = self.stop()
        if not stop_result["success"] and "متوقف بالفعل" not in stop_result["message"]:
            return stop_result

        time.sleep(3)  # انتظار للتأكد من الإيقاف الكامل

        start_result = self.start()
        if start_result["success"]:
            return {"success": True, "message": "🔄 تم إعادة تشغيل n8n بنجاح"}
        else:
            return start_result
    
    def get_logs(self, lines=50):
        """الحصول على سجلات n8n"""
        result = self.run_command(f"docker-compose logs --tail {lines} n8n")
        if result["success"]:
            return {"success": True, "logs": result["output"]}
        else:
            return {"success": False, "logs": f"فشل في الحصول على السجلات: {result['error']}"}

# إنشاء مدير n8n
manager = N8nManager()

HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدير n8n</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 700px;
            width: 100%;
            text-align: center;
        }
        
        .logo {
            font-size: 3em;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: bold;
            font-size: 1.2em;
        }
        
        .status.running {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .status.stopped {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .status.starting {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        button {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 130px;
        }
        
        .start-btn {
            background: #28a745;
            color: white;
        }
        
        .start-btn:hover:not(:disabled) {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .stop-btn {
            background: #dc3545;
            color: white;
        }
        
        .stop-btn:hover:not(:disabled) {
            background: #c82333;
            transform: translateY(-2px);
        }
        
        .restart-btn {
            background: #ffc107;
            color: #212529;
        }
        
        .restart-btn:hover:not(:disabled) {
            background: #e0a800;
            transform: translateY(-2px);
        }
        
        .open-btn {
            background: #007bff;
            color: white;
        }
        
        .open-btn:hover:not(:disabled) {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .logs-btn {
            background: #6c757d;
            color: white;
        }
        
        .logs-btn:hover:not(:disabled) {
            background: #545b62;
            transform: translateY(-2px);
        }
        
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .info {
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: right;
        }
        
        .info h3 {
            color: #495057;
            margin-bottom: 10px;
        }
        
        .info p {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 5px;
        }
        
        .logs {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
            text-align: left;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            display: none;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🔄</div>
        <h1>مدير n8n</h1>
        
        <div id="status" class="status">
            جاري التحقق من حالة n8n... <div class="spinner"></div>
        </div>
        
        <div class="buttons">
            <button id="startBtn" class="start-btn" onclick="startN8n()">▶️ تشغيل</button>
            <button id="stopBtn" class="stop-btn" onclick="stopN8n()">⏹️ إيقاف</button>
            <button id="restartBtn" class="restart-btn" onclick="restartN8n()">🔄 إعادة تشغيل</button>
            <button id="openBtn" class="open-btn" onclick="openN8n()">🌐 فتح n8n</button>
            <button id="logsBtn" class="logs-btn" onclick="toggleLogs()">📋 السجلات</button>
        </div>
        
        <div class="info">
            <h3>معلومات مهمة:</h3>
            <p>• جميع المشاريع والبيانات محفوظة تلقائياً</p>
            <p>• يمكنك إيقاف وتشغيل n8n دون فقدان أي بيانات</p>
            <p>• الرابط: <a href="http://localhost:5678" target="_blank">http://localhost:5678</a></p>
            <p>• البيانات محفوظة في: ./workflows و ./credentials</p>
            <p>• الإصدار: أحدث إصدار من n8n</p>
        </div>
        
        <div id="logs" class="logs">
            <strong>السجلات:</strong><br>
            <div id="logContent">جاري تحميل السجلات...</div>
        </div>
    </div>

    <script>
        let isLoading = false;

        async function checkStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                updateStatus(data.status, data.message);
                updateButtons(data.status);
            } catch (error) {
                updateStatus('error', 'خطأ في الاتصال بالخادم');
                updateButtons('error');
            }
        }

        function updateStatus(status, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${status}`;
            
            if (status === 'starting' || isLoading) {
                statusEl.innerHTML = message + ' <div class="spinner"></div>';
            } else {
                statusEl.innerHTML = message;
            }
        }

        function updateButtons(status) {
            const isRunning = status === 'running';
            const isStarting = status === 'starting';
            
            document.getElementById('startBtn').disabled = isRunning || isStarting || isLoading;
            document.getElementById('stopBtn').disabled = (!isRunning && !isStarting) || isLoading;
            document.getElementById('restartBtn').disabled = (!isRunning && !isStarting) || isLoading;
            document.getElementById('openBtn').disabled = !isRunning || isLoading;
            document.getElementById('logsBtn').disabled = isLoading;
        }

        async function executeAction(action, loadingMessage) {
            if (isLoading) return;

            isLoading = true;
            updateStatus('starting', loadingMessage);
            updateButtons('starting');

            try {
                const response = await fetch(`/api/${action}`, { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    // عرض رسالة النجاح مؤقتاً
                    updateStatus('running', data.message);
                    // ثم التحقق من الحالة الفعلية بعد فترة
                    setTimeout(checkStatus, 3000);
                } else {
                    updateStatus('error', data.message);
                    // التحقق من الحالة حتى في حالة الفشل
                    setTimeout(checkStatus, 2000);
                }
            } catch (error) {
                updateStatus('error', 'خطأ في تنفيذ العملية');
                setTimeout(checkStatus, 2000);
            } finally {
                isLoading = false;
            }
        }

        async function startN8n() {
            await executeAction('start', 'جاري تشغيل n8n...');
        }

        async function stopN8n() {
            await executeAction('stop', 'جاري إيقاف n8n...');
        }

        async function restartN8n() {
            await executeAction('restart', 'جاري إعادة تشغيل n8n...');
        }

        function openN8n() {
            window.open('http://localhost:5678', '_blank');
        }

        async function toggleLogs() {
            const logsDiv = document.getElementById('logs');
            const logContent = document.getElementById('logContent');
            
            if (logsDiv.style.display === 'none' || !logsDiv.style.display) {
                logsDiv.style.display = 'block';
                logContent.innerHTML = 'جاري تحميل السجلات...';
                
                try {
                    const response = await fetch('/api/logs');
                    const data = await response.json();
                    
                    if (data.success) {
                        logContent.innerHTML = data.logs.replace(/\\n/g, '<br>');
                    } else {
                        logContent.innerHTML = 'فشل في تحميل السجلات: ' + data.logs;
                    }
                } catch (error) {
                    logContent.innerHTML = 'خطأ في تحميل السجلات';
                }
            } else {
                logsDiv.style.display = 'none';
            }
        }

        // التحقق من الحالة كل 5 ثوان
        setInterval(checkStatus, 5000);
        
        // التحقق الأولي
        checkStatus();
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/status')
def api_status():
    return jsonify(manager.check_status())

@app.route('/api/start', methods=['POST'])
def api_start():
    return jsonify(manager.start())

@app.route('/api/stop', methods=['POST'])
def api_stop():
    return jsonify(manager.stop())

@app.route('/api/restart', methods=['POST'])
def api_restart():
    return jsonify(manager.restart())

@app.route('/api/logs')
def api_logs():
    return jsonify(manager.get_logs())

@app.route('/favicon.ico')
def favicon():
    return '', 204

@app.errorhandler(404)
def not_found(error):
    return jsonify({"error": "الصفحة غير موجودة"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"error": "خطأ داخلي في الخادم"}), 500

if __name__ == '__main__':
    try:
        print("🚀 بدء تشغيل مدير n8n...")
        print("📱 افتح المتصفح على: http://localhost:8080")
        print("⚡ للإيقاف اضغط Ctrl+C")
        print("=" * 50)

        # التحقق من المنفذ
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 8080))
        sock.close()

        if result == 0:
            print("⚠️  تحذير: المنفذ 8080 مستخدم بالفعل")
            print("جاري المحاولة على منفذ 8081...")
            port = 8081
        else:
            port = 8080

        app.run(host='0.0.0.0', port=port, debug=False, threaded=True)

    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف المدير بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل المدير: {e}")
    finally:
        print("👋 شكراً لاستخدام مدير n8n")

#!/usr/bin/env python3
"""
دمج الفيديو والصوت - معالجة متقدمة لدمج مقاطع Veo 3 مع الصوت المولد
"""

import os
import sys
import json
import requests
import subprocess
from pathlib import Path
from typing import Optional, Dict, Tuple
import logging
import tempfile
import shutil

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AudioVideoMerger:
    def __init__(self, temp_dir: str = "/tmp/n8n_video_processing"):
        self.temp_dir = Path(temp_dir)
        self.temp_dir.mkdir(exist_ok=True)
        self.output_dir = Path("/data/output")
        self.output_dir.mkdir(exist_ok=True)
    
    def download_video(self, video_url: str, filename: str) -> Optional[Path]:
        """تحميل الفيديو من الرابط"""
        try:
            logger.info(f"تحميل الفيديو من: {video_url}")
            
            response = requests.get(video_url, stream=True, timeout=300)
            response.raise_for_status()
            
            video_path = self.temp_dir / filename
            
            with open(video_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info(f"تم تحميل الفيديو: {video_path}")
            return video_path
            
        except Exception as e:
            logger.error(f"فشل في تحميل الفيديو: {e}")
            return None
    
    def save_audio_data(self, audio_data: bytes, filename: str) -> Optional[Path]:
        """حفظ بيانات الصوت"""
        try:
            audio_path = self.temp_dir / filename
            
            with open(audio_path, 'wb') as f:
                f.write(audio_data)
            
            logger.info(f"تم حفظ الصوت: {audio_path}")
            return audio_path
            
        except Exception as e:
            logger.error(f"فشل في حفظ الصوت: {e}")
            return None
    
    def get_video_info(self, video_path: Path) -> Dict:
        """الحصول على معلومات الفيديو"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', str(video_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            info = json.loads(result.stdout)
            
            video_stream = next((s for s in info['streams'] if s['codec_type'] == 'video'), None)
            audio_stream = next((s for s in info['streams'] if s['codec_type'] == 'audio'), None)
            
            return {
                'duration': float(info['format']['duration']),
                'has_video': video_stream is not None,
                'has_audio': audio_stream is not None,
                'width': int(video_stream['width']) if video_stream else 0,
                'height': int(video_stream['height']) if video_stream else 0,
                'fps': eval(video_stream['r_frame_rate']) if video_stream else 0
            }
            
        except Exception as e:
            logger.error(f"فشل في الحصول على معلومات الفيديو: {e}")
            return {}
    
    def optimize_audio_for_video(self, audio_path: Path, target_duration: float) -> Optional[Path]:
        """تحسين الصوت ليناسب مدة الفيديو"""
        try:
            optimized_path = self.temp_dir / f"optimized_{audio_path.name}"
            
            # قطع أو تكرار الصوت ليطابق مدة الفيديو
            cmd = [
                'ffmpeg', '-y', '-i', str(audio_path),
                '-t', str(target_duration),
                '-af', 'afade=in:st=0:d=0.5,afade=out:st={}:d=0.5'.format(max(0, target_duration - 0.5)),
                '-ar', '44100', '-ac', '2', '-b:a', '128k',
                str(optimized_path)
            ]
            
            subprocess.run(cmd, check=True, capture_output=True)
            logger.info(f"تم تحسين الصوت: {optimized_path}")
            return optimized_path
            
        except Exception as e:
            logger.error(f"فشل في تحسين الصوت: {e}")
            return audio_path
    
    def add_background_music(self, main_audio: Path, video_duration: float) -> Optional[Path]:
        """إضافة موسيقى خلفية خفيفة"""
        try:
            # قائمة بالموسيقى الخلفية المجانية (يجب تحميلها مسبقاً)
            background_music_dir = Path("/data/background_music")
            if not background_music_dir.exists():
                logger.info("لا توجد موسيقى خلفية متاحة")
                return main_audio
            
            music_files = list(background_music_dir.glob("*.mp3"))
            if not music_files:
                return main_audio
            
            # اختيار موسيقى عشوائية
            import random
            selected_music = random.choice(music_files)
            
            mixed_audio = self.temp_dir / f"mixed_{main_audio.name}"
            
            # دمج الصوت الرئيسي مع الموسيقى الخلفية
            cmd = [
                'ffmpeg', '-y',
                '-i', str(main_audio),
                '-i', str(selected_music),
                '-filter_complex',
                f'[1:a]volume=0.2,aloop=loop=-1:size=2e+09[bg];[0:a][bg]amix=inputs=2:duration=first[a]',
                '-map', '[a]',
                '-t', str(video_duration),
                '-ar', '44100', '-ac', '2', '-b:a', '128k',
                str(mixed_audio)
            ]
            
            subprocess.run(cmd, check=True, capture_output=True)
            logger.info(f"تم دمج الموسيقى الخلفية: {mixed_audio}")
            return mixed_audio
            
        except Exception as e:
            logger.warning(f"فشل في إضافة الموسيقى الخلفية: {e}")
            return main_audio
    
    def merge_video_audio(self, video_path: Path, audio_path: Path, output_name: str, quality: str = "high") -> Optional[Path]:
        """دمج الفيديو مع الصوت"""
        try:
            output_path = self.output_dir / output_name
            
            # الحصول على معلومات الفيديو
            video_info = self.get_video_info(video_path)
            if not video_info:
                raise Exception("فشل في الحصول على معلومات الفيديو")
            
            # تحسين الصوت
            optimized_audio = self.optimize_audio_for_video(audio_path, video_info['duration'])
            
            # إضافة موسيقى خلفية إذا أمكن
            final_audio = self.add_background_music(optimized_audio, video_info['duration'])
            
            # إعدادات الجودة
            quality_settings = {
                "high": {
                    "video_codec": "libx264",
                    "video_bitrate": "2M",
                    "audio_codec": "aac",
                    "audio_bitrate": "128k",
                    "preset": "medium",
                    "crf": "23"
                },
                "medium": {
                    "video_codec": "libx264",
                    "video_bitrate": "1M",
                    "audio_codec": "aac",
                    "audio_bitrate": "96k",
                    "preset": "fast",
                    "crf": "28"
                },
                "low": {
                    "video_codec": "libx264",
                    "video_bitrate": "500k",
                    "audio_codec": "aac",
                    "audio_bitrate": "64k",
                    "preset": "ultrafast",
                    "crf": "32"
                }
            }
            
            settings = quality_settings.get(quality, quality_settings["high"])
            
            # أمر دمج الفيديو والصوت
            cmd = [
                'ffmpeg', '-y',
                '-i', str(video_path),
                '-i', str(final_audio),
                '-c:v', settings["video_codec"],
                '-preset', settings["preset"],
                '-crf', settings["crf"],
                '-b:v', settings["video_bitrate"],
                '-c:a', settings["audio_codec"],
                '-b:a', settings["audio_bitrate"],
                '-ar', '44100',
                '-ac', '2',
                '-shortest',
                '-movflags', '+faststart',  # للتشغيل السريع على الويب
                '-pix_fmt', 'yuv420p',  # للتوافق مع جميع المشغلات
                str(output_path)
            ]
            
            logger.info(f"بدء دمج الفيديو والصوت...")
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            # التحقق من نجاح العملية
            if output_path.exists() and output_path.stat().st_size > 0:
                logger.info(f"تم دمج الفيديو بنجاح: {output_path}")
                
                # تنظيف الملفات المؤقتة
                self.cleanup_temp_files([video_path, audio_path, optimized_audio, final_audio])
                
                return output_path
            else:
                raise Exception("فشل في إنشاء الملف النهائي")
                
        except subprocess.CalledProcessError as e:
            logger.error(f"فشل في دمج الفيديو: {e.stderr}")
            return None
        except Exception as e:
            logger.error(f"خطأ في دمج الفيديو: {e}")
            return None
    
    def cleanup_temp_files(self, file_paths: list):
        """تنظيف الملفات المؤقتة"""
        for file_path in file_paths:
            try:
                if file_path and file_path.exists():
                    file_path.unlink()
                    logger.debug(f"تم حذف الملف المؤقت: {file_path}")
            except Exception as e:
                logger.warning(f"فشل في حذف الملف المؤقت {file_path}: {e}")
    
    def process_video_audio_merge(self, video_url: str, audio_data: bytes, output_name: str, quality: str = "high") -> Dict:
        """معالجة شاملة لدمج الفيديو والصوت"""
        try:
            # تحميل الفيديو
            video_filename = f"video_{int(time.time())}.mp4"
            video_path = self.download_video(video_url, video_filename)
            if not video_path:
                return {"success": False, "error": "فشل في تحميل الفيديو"}
            
            # حفظ الصوت
            audio_filename = f"audio_{int(time.time())}.mp3"
            audio_path = self.save_audio_data(audio_data, audio_filename)
            if not audio_path:
                return {"success": False, "error": "فشل في حفظ الصوت"}
            
            # دمج الفيديو والصوت
            final_video = self.merge_video_audio(video_path, audio_path, output_name, quality)
            if not final_video:
                return {"success": False, "error": "فشل في دمج الفيديو والصوت"}
            
            return {
                "success": True,
                "output_path": str(final_video),
                "file_size": final_video.stat().st_size,
                "message": "تم دمج الفيديو والصوت بنجاح"
            }
            
        except Exception as e:
            logger.error(f"خطأ في معالجة دمج الفيديو: {e}")
            return {"success": False, "error": str(e)}

def main():
    """الدالة الرئيسية للتشغيل من سطر الأوامر"""
    import time
    
    # قراءة المتغيرات من البيئة
    video_url = os.getenv('VIDEO_URL')
    audio_file = os.getenv('AUDIO_FILE')
    output_name = os.getenv('OUTPUT_NAME', f'merged_video_{int(time.time())}.mp4')
    quality = os.getenv('QUALITY', 'high')
    
    if not video_url:
        print(json.dumps({"success": False, "error": "VIDEO_URL غير محدد"}))
        sys.exit(1)
    
    if not audio_file:
        print(json.dumps({"success": False, "error": "AUDIO_FILE غير محدد"}))
        sys.exit(1)
    
    # قراءة بيانات الصوت
    try:
        with open(audio_file, 'rb') as f:
            audio_data = f.read()
    except Exception as e:
        print(json.dumps({"success": False, "error": f"فشل في قراءة ملف الصوت: {e}"}))
        sys.exit(1)
    
    # إنشاء المدمج ومعالجة الطلب
    merger = AudioVideoMerger()
    result = merger.process_video_audio_merge(video_url, audio_data, output_name, quality)
    
    print(json.dumps(result, ensure_ascii=False))

if __name__ == "__main__":
    main()

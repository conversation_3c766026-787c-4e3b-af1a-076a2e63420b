{
  "name": "Enhanced AI Video Generator with Veo 3 Manager",
  "nodes": [
    {
      "parameters": {
        "rule": {
          "interval": [
            {
              "field": "hours",
              "hoursInterval": 3
            }
          ]
        }
      },
      "id": "schedule-trigger",
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "command": "python3",
        "arguments": "veo3_manager.py --check-status",
        "options": {
          "cwd": "/data"
        }
      },
      "id": "check-veo3-status",
      "name": "Check Veo3 Status",
      "type": "n8n-nodes-base.executeCommand",
      "typeVersion": 1,
      "position": [460, 300]
    },
    {
      "parameters": {
        "jsCode": "// قائمة الأشياء المشهورة للتقطيع مع تفاصيل إضافية\nconst cuttingItems = [\n  {\n    name: 'كوكب المشتري',\n    englishName: 'Jupiter planet',\n    category: 'space',\n    difficulty: 'hard'\n  },\n  {\n    name: 'تفاحة ذهبية',\n    englishName: 'golden apple',\n    category: 'fruit',\n    difficulty: 'easy'\n  },\n  {\n    name: 'هاتف آيفون',\n    englishName: 'iPhone smartphone',\n    category: 'technology',\n    difficulty: 'medium'\n  },\n  {\n    name: 'فاكهة الشيطان من ون بيس',\n    englishName: 'Devil Fruit from One Piece anime',\n    category: 'anime',\n    difficulty: 'hard'\n  },\n  {\n    name: 'برج إيفل مصغر',\n    englishName: 'miniature Eiffel Tower',\n    category: 'landmark',\n    difficulty: 'medium'\n  },\n  {\n    name: 'كعكة عيد ميلاد',\n    englishName: 'birthday cake',\n    category: 'food',\n    difficulty: 'easy'\n  },\n  {\n    name: 'سيارة تسلا مصغرة',\n    englishName: 'miniature Tesla car',\n    category: 'vehicle',\n    difficulty: 'hard'\n  },\n  {\n    name: 'ساعة رولكس',\n    englishName: 'Rolex watch',\n    category: 'luxury',\n    difficulty: 'medium'\n  },\n  {\n    name: 'شوكولاتة فيريرو روشيه',\n    englishName: 'Ferrero Rocher chocolate',\n    category: 'candy',\n    difficulty: 'easy'\n  },\n  {\n    name: 'كرة التنين من دراغون بول',\n    englishName: 'Dragon Ball from Dragon Ball anime',\n    category: 'anime',\n    difficulty: 'hard'\n  }\n];\n\n// اختيار عنصر عشوائي\nconst randomItem = cuttingItems[Math.floor(Math.random() * cuttingItems.length)];\n\n// إنشاء prompt متقدم للفيديو\nconst videoPrompt = `Professional food photography setup: A chef's hand wearing a black glove grips a gleaming stainless steel chef's knife. On a rustic wooden cutting board sits a perfectly positioned ${randomItem.englishName}. Dramatic side lighting creates deep shadows and highlights. The knife descends in ultra-slow motion, slicing through the ${randomItem.englishName} with surgical precision. The camera captures the exact moment of the cut in extreme close-up, revealing the intricate interior details. Shot in 4K resolution at 120fps for buttery smooth slow motion. Cinematic color grading with warm tones.`;\n\n// إنشاء prompt للمحتوى\nconst contentPrompt = `أنشئ عنوان جذاب وهاشتاقات لفيديو TikTok يظهر تقطيع ${randomItem.name} بالسكين.\n\nالمتطلبات:\n- العنوان: قصير وجذاب (أقل من 50 حرف) مع إيموجي\n- 10 هاشتاقات: 5 بالعربية و 5 بالإنجليزية\n- التركيز على الترند والانتشار\n- استخدام كلمات مثل: تقطيع، مهارات، مثير، satisfying\n\nالتنسيق:\nالعنوان: [العنوان هنا]\nالهاشتاقات: [الهاشتاقات مفصولة بمسافات]`;\n\nreturn {\n  selectedItem: randomItem,\n  videoPrompt: videoPrompt,\n  contentPrompt: contentPrompt,\n  timestamp: new Date().toISOString(),\n  category: randomItem.category,\n  difficulty: randomItem.difficulty\n};"
      },
      "id": "enhanced-content-generator",
      "name": "Enhanced Content Generator",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [680, 300]
    },
    {
      "parameters": {
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "googleGenerativeAiApi",
        "resource": "text",
        "operation": "generate",
        "prompt": "={{ $json.contentPrompt }}",
        "options": {
          "model": "gemini-1.5-pro",
          "temperature": 0.8,
          "maxTokens": 300
        }
      },
      "id": "gemini-content-enhanced",
      "name": "Gemini - Enhanced Content",
      "type": "n8n-nodes-base.googleGenerativeAi",
      "typeVersion": 1,
      "position": [900, 300]
    },
    {
      "parameters": {
        "jsCode": "// معالجة محسنة للنص المولد من Gemini\nconst generatedText = $input.first().json.text || '';\nconst lines = generatedText.split('\\n').filter(line => line.trim());\n\nlet title = '';\nlet hashtags = [];\n\n// استخراج العنوان والهاشتاقات بطريقة محسنة\nfor (const line of lines) {\n  const trimmedLine = line.trim();\n  \n  if (trimmedLine.includes('العنوان:') || trimmedLine.includes('Title:')) {\n    title = trimmedLine.split(':')[1]?.trim() || '';\n  } else if (trimmedLine.includes('الهاشتاقات:') || trimmedLine.includes('Hashtags:')) {\n    const hashtagsText = trimmedLine.split(':')[1]?.trim() || '';\n    hashtags = hashtagsText.split(/\\s+/).filter(tag => tag.startsWith('#'));\n  } else if (trimmedLine.startsWith('#')) {\n    hashtags.push(trimmedLine);\n  } else if (!title && !trimmedLine.includes(':')) {\n    title = trimmedLine;\n  }\n}\n\n// تنظيف العنوان\ntitle = title.replace(/[\"']/g, '').trim();\n\n// إضافة هاشتاقات افتراضية محسنة إذا لم توجد\nif (hashtags.length === 0) {\n  const itemCategory = $('Enhanced Content Generator').first().json.category;\n  const baseHashtags = [\n    '#تقطيع', '#مقاطع_قصيرة', '#ترند', '#مهارات', '#مثير',\n    '#cutting', '#satisfying', '#viral', '#trending', '#skills'\n  ];\n  \n  // إضافة هاشتاقات خاصة بالفئة\n  const categoryHashtags = {\n    'food': ['#طبخ', '#طعام', '#food', '#cooking'],\n    'technology': ['#تكنولوجيا', '#تقنية', '#tech', '#gadgets'],\n    'anime': ['#أنمي', '#انمي', '#anime', '#manga'],\n    'luxury': ['#فخامة', '#رفاهية', '#luxury', '#premium'],\n    'space': ['#فضاء', '#كواكب', '#space', '#planets']\n  };\n  \n  hashtags = [...baseHashtags, ...(categoryHashtags[itemCategory] || [])];\n}\n\n// دمج البيانات من العقدة السابقة\nconst previousData = $('Enhanced Content Generator').first().json;\n\nreturn {\n  ...previousData,\n  title: title || `تقطيع ${previousData.selectedItem.name} بطريقة مثيرة! 🔪✨`,\n  hashtags: hashtags.slice(0, 15).join(' '), // الحد الأقصى 15 هاشتاق\n  hashtagsArray: hashtags.slice(0, 15),\n  contentReady: true\n};"
      },
      "id": "enhanced-content-processor",
      "name": "Enhanced Content Processor",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1120, 300]
    },
    {
      "parameters": {\n        \"command\": \"python3\",\n        \"arguments\": \"veo3_manager.py --generate-video\",\n        \"options\": {\n          \"cwd\": \"/data\",\n          \"env\": {\n            \"VIDEO_PROMPT\": \"={{ $json.videoPrompt }}\",\n            \"ITEM_NAME\": \"={{ $json.selectedItem.name }}\"\n          }\n        }\n      },\n      \"id\": \"veo3-video-generator\",\n      \"name\": \"Veo3 Video Generator\",\n      \"type\": \"n8n-nodes-base.executeCommand\",\n      \"typeVersion\": 1,\n      \"position\": [1340, 300]\n    }\n  ],\n  \"connections\": {\n    \"Schedule Trigger\": {\n      \"main\": [[\n        {\n          \"node\": \"Check Veo3 Status\",\n          \"type\": \"main\",\n          \"index\": 0\n        }\n      ]]\n    },\n    \"Check Veo3 Status\": {\n      \"main\": [[\n        {\n          \"node\": \"Enhanced Content Generator\",\n          \"type\": \"main\",\n          \"index\": 0\n        }\n      ]]\n    },\n    \"Enhanced Content Generator\": {\n      \"main\": [[\n        {\n          \"node\": \"Gemini - Enhanced Content\",\n          \"type\": \"main\",\n          \"index\": 0\n        }\n      ]]\n    },\n    \"Gemini - Enhanced Content\": {\n      \"main\": [[\n        {\n          \"node\": \"Enhanced Content Processor\",\n          \"type\": \"main\",\n          \"index\": 0\n        }\n      ]]\n    },\n    \"Enhanced Content Processor\": {\n      \"main\": [[\n        {\n          \"node\": \"Veo3 Video Generator\",\n          \"type\": \"main\",\n          \"index\": 0\n        }\n      ]]\n    }\n  },\n  \"pinData\": {},\n  \"settings\": {\n    \"executionOrder\": \"v1\"\n  },\n  \"staticData\": null,\n  \"tags\": [\"ai\", \"video\", \"veo3\", \"tiktok\"],\n  \"triggerCount\": 0,\n  \"updatedAt\": \"2024-12-19T12:00:00.000Z\",\n  \"versionId\": \"2\"\n}

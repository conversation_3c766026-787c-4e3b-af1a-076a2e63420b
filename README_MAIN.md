# مدير n8n الرئيسي

ملف تشغيل رئيسي بسيط وفعال لإدارة n8n.

## المميزات

✅ **تشغيل تلقائي**: يقوم بتشغيل n8n تلقائياً  
✅ **إيقاف آمن**: يوقف n8n بأمان عند إغلاق البرنامج  
✅ **فتح المتصفح**: يفتح المتصفح تلقائياً عند جاهزية n8n  
✅ **مراقبة مستمرة**: يراقب حالة n8n ويعرض التحديثات  
✅ **دعم متعدد**: يدعم Docker و n8n المحلي  
✅ **سهولة الاستخدام**: تشغيل بنقرة واحدة  

## طرق التشغيل

### Windows
```bash
# الطريقة الأولى: ملف batch
run_n8n.bat

# الطريقة الثانية: Python مباشرة
python main.py
```

### Linux/Mac
```bash
# الطريقة الأولى: shell script
./run_n8n.sh

# الطريقة الثانية: Python مباشرة
python3 main.py
```

## المتطلبات

### الأساسية
- Python 3.6 أو أحدث
- مكتبة requests: `pip install requests`

### لاستخدام Docker (مفضل)
- Docker Desktop
- ملف docker-compose.yml

### لاستخدام n8n المحلي (بديل)
- Node.js
- n8n مثبت: `npm install -g n8n`

## كيفية العمل

1. **التحقق من المتطلبات**: يتحقق من وجود Docker أو n8n المحلي
2. **اختيار الطريقة**: يختار Docker إذا كان متاحاً، وإلا n8n المحلي
3. **تشغيل n8n**: يبدأ تشغيل n8n
4. **انتظار الجاهزية**: ينتظر حتى يصبح n8n جاهز
5. **فتح المتصفح**: يفتح المتصفح تلقائياً على http://localhost:5678
6. **المراقبة**: يراقب حالة n8n باستمرار
7. **الإيقاف الآمن**: عند الضغط على Ctrl+C يوقف n8n بأمان

## الاستخدام

1. شغل الملف:
   - Windows: انقر مرتين على `run_n8n.bat`
   - Linux/Mac: شغل `./run_n8n.sh`

2. انتظر حتى يظهر "n8n جاهز للاستخدام!"

3. سيفتح المتصفح تلقائياً على n8n

4. للإيقاف: اضغط `Ctrl+C`

## الرسائل المهمة

- ✅ **أخضر**: عمليات ناجحة
- ⚠️ **أصفر**: تحذيرات
- ❌ **أحمر**: أخطاء
- 🔄 **أزرق**: عمليات جارية
- 💡 **أبيض**: نصائح ومعلومات

## حل المشاكل

### Docker لا يعمل
```
❌ Docker غير متوفر أو لا يعمل
💡 تأكد من تشغيل Docker Desktop
```

### n8n لا يستجيب
```
⚠️ انتهت مهلة الانتظار، لكن n8n قد يكون لا يزال يبدأ
💡 افتح المتصفح يدوياً على: http://localhost:5678
```

### لا يمكن تشغيل n8n
```
❌ لا يمكن تشغيل n8n:
  • Docker غير متوفر أو لا يعمل
  • ملف docker-compose.yml غير موجود
  • n8n غير مثبت محلياً

💡 الحلول المقترحة:
  1. تثبيت وتشغيل Docker Desktop
  2. أو تثبيت n8n محلياً: npm install -g n8n
```

## البيانات

جميع البيانات محفوظة تلقائياً:
- **Docker**: في volumes المحددة في docker-compose.yml
- **محلي**: في مجلد .n8n في home directory

## الدعم

إذا واجهت مشاكل:
1. تأكد من تثبيت المتطلبات
2. تحقق من رسائل الخطأ
3. جرب إعادة تشغيل Docker Desktop
4. تأكد من عدم استخدام المنفذ 5678 من برنامج آخر

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدير n8n</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        
        .logo {
            font-size: 3em;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: bold;
            font-size: 1.2em;
        }
        
        .status.running {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .status.stopped {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .status.loading {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }
        
        .buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        button {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }
        
        .start-btn {
            background: #28a745;
            color: white;
        }
        
        .start-btn:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .stop-btn {
            background: #dc3545;
            color: white;
        }
        
        .stop-btn:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
        
        .restart-btn {
            background: #ffc107;
            color: #212529;
        }
        
        .restart-btn:hover {
            background: #e0a800;
            transform: translateY(-2px);
        }
        
        .open-btn {
            background: #007bff;
            color: white;
        }
        
        .open-btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }
        
        .info {
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: right;
        }
        
        .info h3 {
            color: #495057;
            margin-bottom: 10px;
        }
        
        .info p {
            color: #6c757d;
            line-height: 1.6;
        }
        
        .logs {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            max-height: 200px;
            overflow-y: auto;
            text-align: left;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🔄</div>
        <h1>مدير n8n</h1>
        
        <div id="status" class="status loading">
            <div class="spinner"></div>
            جاري التحقق من حالة n8n...
        </div>
        
        <div class="buttons">
            <button id="startBtn" class="start-btn" onclick="startN8n()">▶️ تشغيل n8n</button>
            <button id="stopBtn" class="stop-btn" onclick="stopN8n()">⏹️ إيقاف n8n</button>
            <button id="restartBtn" class="restart-btn" onclick="restartN8n()">🔄 إعادة تشغيل</button>
            <button id="openBtn" class="open-btn" onclick="openN8n()">🌐 فتح n8n</button>
        </div>
        
        <div class="info">
            <h3>معلومات مهمة:</h3>
            <p>• جميع المشاريع والبيانات محفوظة في مجلد البيانات</p>
            <p>• يمكنك إيقاف وتشغيل n8n دون فقدان أي بيانات</p>
            <p>• الرابط: http://localhost:5678</p>
            <p>• البيانات محفوظة في: ./workflows و ./credentials</p>
        </div>
        
        <div id="logs" class="logs" style="display: none;">
            <strong>السجلات:</strong><br>
            <div id="logContent"></div>
        </div>
    </div>

    <script>
        let isLoading = false;

        async function checkStatus() {
            try {
                const response = await fetch('http://localhost:5678', { 
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                updateStatus('running', '✅ n8n يعمل بشكل طبيعي');
                updateButtons(true);
            } catch (error) {
                updateStatus('stopped', '❌ n8n متوقف');
                updateButtons(false);
            }
        }

        function updateStatus(status, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${status}`;
            statusEl.innerHTML = message;
        }

        function updateButtons(isRunning) {
            document.getElementById('startBtn').disabled = isRunning || isLoading;
            document.getElementById('stopBtn').disabled = !isRunning || isLoading;
            document.getElementById('restartBtn').disabled = !isRunning || isLoading;
            document.getElementById('openBtn').disabled = !isRunning || isLoading;
        }

        async function executeCommand(command, successMessage, errorMessage) {
            if (isLoading) return;
            
            isLoading = true;
            updateStatus('loading', '🔄 جاري التنفيذ...');
            updateButtons(false);
            
            try {
                // محاكاة تنفيذ الأمر (في التطبيق الحقيقي ستحتاج لـ backend)
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                updateStatus('running', successMessage);
                setTimeout(checkStatus, 1000);
            } catch (error) {
                updateStatus('stopped', errorMessage);
            } finally {
                isLoading = false;
            }
        }

        async function startN8n() {
            await executeCommand(
                'docker-compose up -d',
                '✅ تم تشغيل n8n بنجاح',
                '❌ فشل في تشغيل n8n'
            );
        }

        async function stopN8n() {
            await executeCommand(
                'docker-compose down',
                '⏹️ تم إيقاف n8n (البيانات محفوظة)',
                '❌ فشل في إيقاف n8n'
            );
        }

        async function restartN8n() {
            await executeCommand(
                'docker-compose restart',
                '🔄 تم إعادة تشغيل n8n بنجاح',
                '❌ فشل في إعادة تشغيل n8n'
            );
        }

        function openN8n() {
            window.open('http://localhost:5678', '_blank');
        }

        // التحقق من الحالة كل 10 ثوان
        setInterval(checkStatus, 10000);
        
        // التحقق الأولي
        checkStatus();
    </script>
</body>
</html>

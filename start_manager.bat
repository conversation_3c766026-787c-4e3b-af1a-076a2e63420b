@echo off
chcp 65001 >nul
title مدير n8n

echo.
echo ========================================
echo           مدير n8n
echo ========================================
echo.
echo اختر نوع المدير:
echo 1. مدير رسومي (واجهة ويب)
echo 2. مدير نصي (سطر الأوامر)
echo.

set /p choice="اختر (1 أو 2): "

if "%choice%"=="1" goto web_manager
if "%choice%"=="2" goto cli_manager

echo ❌ اختيار غير صحيح
pause
exit /b 1

:web_manager
echo.
echo 🌐 تشغيل المدير الرسومي...

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت. يرجى تثبيت Python أولاً.
    echo يمكنك تحميله من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM تثبيت المكتبات المطلوبة
echo 📦 تثبيت المكتبات المطلوبة...
pip install flask requests >nul 2>&1

if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المكتبات المطلوبة
    echo جاري المحاولة مرة أخرى...
    pip install flask requests
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات. تحقق من اتصال الإنترنت.
        pause
        exit /b 1
    )
)

echo ✅ تم تثبيت المكتبات بنجاح
echo.
echo 🚀 بدء تشغيل المدير الرسومي...
echo 📱 افتح المتصفح على: http://localhost:8080
echo ⚡ للإيقاف اضغط Ctrl+C
echo.

python n8n_manager.py
goto end

:cli_manager
echo.
echo 💻 تشغيل المدير النصي...

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت. يرجى تثبيت Python أولاً.
    echo يمكنك تحميله من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM تثبيت المكتبة المطلوبة
echo � تثبيت المكتبة المطلوبة...
pip install requests >nul 2>&1

echo ✅ جاهز للاستخدام
echo.

python n8n_control.py
goto end

:end
pause

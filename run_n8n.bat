@echo off
chcp 65001 >nul
title مدير n8n الرئيسي

echo.
echo ========================================
echo           مدير n8n الرئيسي
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت. يرجى تثبيت Python أولاً.
    echo يمكنك تحميله من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM التحقق من وجود Docker
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker غير مثبت. يرجى تثبيت Docker Desktop أولاً.
    echo يمكنك تحميله من: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo ✅ Docker متوفر

REM تثبيت المكتبة المطلوبة
echo 📦 تثبيت المكتبة المطلوبة...
pip install requests >nul 2>&1

if %errorlevel% neq 0 (
    echo ⚠️ فشل في تثبيت requests، جاري المحاولة مرة أخرى...
    pip install requests
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبة المطلوبة
        echo جرب تشغيل: pip install requests
        pause
        exit /b 1
    )
)

echo ✅ جاهز للتشغيل
echo.
echo 🚀 تشغيل مدير n8n...
echo 💡 اضغط Ctrl+C لإيقاف n8n والخروج
echo.

REM تشغيل المدير الرئيسي
python main.py

echo.
echo 👋 شكراً لاستخدام مدير n8n
pause

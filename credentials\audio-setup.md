# إعداد الصوت والموسيقى للمشروع

## خدمات الصوت المجانية

### 1. ElevenLabs (محدود مجاني)
- **الموقع**: https://elevenlabs.io/
- **المجاني**: 10,000 حرف شهرياً
- **المميزات**: أصوات طبيعية جداً، دعم العربية

#### إعداد ElevenLabs:
```json
{
  "name": "ElevenLabs API",
  "type": "httpHeaderAuth",
  "data": {
    "name": "xi-api-key",
    "value": "YOUR_ELEVENLABS_API_KEY"
  }
}
```

#### مثال على الاستخدام:
```javascript
const audioRequest = {
  method: "POST",
  url: "https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM",
  headers: {
    "xi-api-key": "YOUR_API_KEY",
    "Content-Type": "application/json"
  },
  body: {
    "text": "شاهد كيف نقطع هذا الشيء بطريقة مثيرة!",
    "voice_settings": {
      "stability": 0.5,
      "similarity_boost": 0.5
    }
  }
};
```

### 2. Google Text-to-Speech (مجاني جزئياً)
- **المجاني**: 1 مليون حرف شهرياً
- **دعم العربية**: ممتاز

#### إعداد Google TTS:
```json
{
  "name": "Google TTS",
  "type": "googleCloudTtsApi",
  "data": {
    "serviceAccountEmail": "<EMAIL>",
    "privateKey": "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
  }
}
```

### 3. Azure Cognitive Services Speech (مجاني)
- **المجاني**: 500,000 حرف شهرياً
- **جودة عالية**: أصوات طبيعية

### 4. خدمات مجانية بديلة:
- **Festival TTS**: مفتوح المصدر
- **eSpeak**: مجاني تماماً
- **Pico TTS**: خفيف وسريع

## موسيقى خلفية مجانية

### 1. Freesound.org
- **الموقع**: https://freesound.org/
- **المحتوى**: أصوات وموسيقى مجانية
- **الترخيص**: Creative Commons

### 2. YouTube Audio Library
- **الموقع**: https://studio.youtube.com/
- **المحتوى**: موسيقى خلفية مجانية
- **بدون حقوق طبع**: آمن للاستخدام

### 3. Pixabay Music
- **الموقع**: https://pixabay.com/music/
- **المحتوى**: موسيقى مجانية عالية الجودة

### 4. Zapsplat (مجاني مع تسجيل)
- **الموقع**: https://zapsplat.com/
- **المحتوى**: مؤثرات صوتية وموسيقى

## دمج الصوت مع الفيديو

### استخدام FFmpeg (مجاني):
```bash
# دمج الفيديو مع الصوت
ffmpeg -i video.mp4 -i audio.mp3 -c:v copy -c:a aac -shortest output.mp4

# إضافة موسيقى خلفية مع تقليل الصوت
ffmpeg -i video.mp4 -i background_music.mp3 -filter_complex "[1:a]volume=0.3[bg];[0:a][bg]amix=inputs=2[a]" -map 0:v -map "[a]" -c:v copy -c:a aac -shortest output.mp4
```

### استخدام MoviePy (Python):
```python
from moviepy.editor import VideoFileClip, AudioFileClip, CompositeAudioClip

def add_audio_to_video(video_path, audio_path, output_path):
    # تحميل الفيديو والصوت
    video = VideoFileClip(video_path)
    audio = AudioFileClip(audio_path)
    
    # قطع الصوت ليطابق مدة الفيديو
    audio = audio.subclip(0, video.duration)
    
    # دمج الصوت مع الفيديو
    final_video = video.set_audio(audio)
    
    # حفظ النتيجة
    final_video.write_videofile(output_path)
    
    # تنظيف الذاكرة
    video.close()
    audio.close()
    final_video.close()
```

## نماذج النصوص الصوتية

### للتقطيع العام:
```
"شاهد كيف نقطع [ITEM] بطريقة مثيرة ومذهلة! هذا المقطع سيجعلك تشعر بالراحة والإعجاب."
```

### للفواكه:
```
"تقطيع مثالي لـ [ITEM]! انظر كيف ينقسم إلى نصفين بشكل مثير للإعجاب."
```

### للتكنولوجيا:
```
"ماذا يحدث عندما نقطع [ITEM]؟ شاهد النتيجة المذهلة!"
```

### للأنمي:
```
"من عالم الأنمي إلى الواقع! شاهد تقطيع [ITEM] بطريقة احترافية."
```

## إعدادات الصوت المثلى

### للفيديوهات القصيرة (TikTok):
- **التردد**: 44.1 kHz
- **البت ريت**: 128 kbps
- **القنوات**: Stereo
- **المدة**: 10-15 ثانية

### تحسين جودة الصوت:
```python
# تطبيق فلاتر تحسين الصوت
def enhance_audio(input_audio, output_audio):
    import librosa
    import soundfile as sf
    
    # تحميل الصوت
    y, sr = librosa.load(input_audio)
    
    # تطبيق فلتر تقليل الضوضاء
    y_filtered = librosa.effects.preemphasis(y)
    
    # تطبيع مستوى الصوت
    y_normalized = librosa.util.normalize(y_filtered)
    
    # حفظ النتيجة
    sf.write(output_audio, y_normalized, sr)
```

## استكشاف الأخطاء

### مشاكل شائعة:

#### 1. جودة الصوت منخفضة:
- استخدم معدل عينات أعلى (44.1 kHz)
- زد البت ريت إلى 192 kbps
- تأكد من جودة النص المدخل

#### 2. عدم تزامن الصوت مع الفيديو:
- تأكد من مطابقة مدة الصوت مع الفيديو
- استخدم `-shortest` في FFmpeg

#### 3. حجم الملف كبير:
- قلل البت ريت إلى 128 kbps
- استخدم ضغط AAC
- قلل معدل العينات إذا لزم الأمر

#### 4. مشاكل في الترميز:
- تأكد من دعم الترميز العربي (UTF-8)
- استخدم نصوص بسيطة وواضحة
- تجنب الرموز الخاصة

## أتمتة عملية الصوت

### سكريبت Python للأتمتة:
```python
import requests
import json
from pathlib import Path

class AudioManager:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://api.elevenlabs.io/v1"
    
    def generate_speech(self, text, voice_id="21m00Tcm4TlvDq8ikWAM"):
        url = f"{self.base_url}/text-to-speech/{voice_id}"
        headers = {
            "xi-api-key": self.api_key,
            "Content-Type": "application/json"
        }
        data = {
            "text": text,
            "voice_settings": {
                "stability": 0.5,
                "similarity_boost": 0.5
            }
        }
        
        response = requests.post(url, headers=headers, json=data)
        return response.content if response.status_code == 200 else None
    
    def save_audio(self, audio_data, filename):
        with open(filename, 'wb') as f:
            f.write(audio_data)
```

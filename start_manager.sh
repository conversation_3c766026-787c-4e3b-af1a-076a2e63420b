#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8

echo ""
echo "========================================"
echo "           مدير n8n"
echo "========================================"
echo ""

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python غير مثبت. يرجى تثبيت Python أولاً."
        echo "Ubuntu/Debian: sudo apt install python3 python3-pip"
        echo "CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "macOS: brew install python3"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# التحقق من وجود pip
if ! command -v pip3 &> /dev/null; then
    if ! command -v pip &> /dev/null; then
        echo "❌ pip غير متوفر."
        exit 1
    else
        PIP_CMD="pip"
    fi
else
    PIP_CMD="pip3"
fi

echo "✅ Python متوفر"
echo ""

# تثبيت المكتبات المطلوبة
echo "📦 تثبيت المكتبات المطلوبة..."
$PIP_CMD install flask requests > /dev/null 2>&1

if [ $? -ne 0 ]; then
    echo "❌ فشل في تثبيت المكتبات المطلوبة"
    echo "جاري المحاولة مرة أخرى..."
    $PIP_CMD install flask requests
    if [ $? -ne 0 ]; then
        echo "❌ فشل في تثبيت المكتبات. تحقق من اتصال الإنترنت."
        exit 1
    fi
fi

echo "✅ تم تثبيت المكتبات بنجاح"
echo ""

# التحقق من وجود Docker
if ! command -v docker &> /dev/null; then
    echo "⚠️  تحذير: Docker غير مثبت أو غير متاح"
    echo "يرجى التأكد من تثبيت Docker وتشغيله"
    echo ""
fi

# التحقق من وجود docker-compose
if ! command -v docker-compose &> /dev/null; then
    echo "⚠️  تحذير: docker-compose غير مثبت أو غير متاح"
    echo "يرجى التأكد من تثبيت docker-compose"
    echo ""
fi

# التحقق من وجود docker-compose.yml
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ ملف docker-compose.yml غير موجود"
    echo "تأكد من وجود الملف في نفس المجلد"
    exit 1
fi

echo "✅ جميع المتطلبات متوفرة"
echo ""
echo "🚀 بدء تشغيل مدير n8n..."
echo "📱 افتح المتصفح على: http://localhost:8080"
echo "⚡ للإيقاف اضغط Ctrl+C"
echo ""

# جعل الملف قابل للتنفيذ
chmod +x "$0"

# تشغيل مدير n8n
$PYTHON_CMD n8n_manager.py

#!/usr/bin/env python3
"""
مدير Veo 3 - إدارة حسابات Google Cloud Skills Boost للوصول المجاني لـ Veo 3
"""

import time
import json
import requests
import subprocess
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Veo3Manager:
    def __init__(self):
        self.current_lab = None
        self.labs_history = []
        self.config_file = "veo3_config.json"
        self.load_config()
    
    def load_config(self):
        """تحميل إعدادات المدير"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except FileNotFoundError:
            self.config = {
                "labs_used": [],
                "current_lab_start_time": None,
                "max_lab_duration": 45,  # دقيقة
                "video_generation_count": 0
            }
            self.save_config()
    
    def save_config(self):
        """حفظ إعدادات المدير"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)
    
    def is_lab_expired(self) -> bool:
        """فحص انتهاء صلاحية المختبر الحالي"""
        if not self.config.get("current_lab_start_time"):
            return True
        
        start_time = datetime.fromisoformat(self.config["current_lab_start_time"])
        current_time = datetime.now()
        duration = (current_time - start_time).total_seconds() / 60
        
        return duration >= self.config["max_lab_duration"]
    
    def get_access_token(self, project_id: str) -> Optional[str]:
        """الحصول على Access Token من gcloud"""
        try:
            # تعيين المشروع
            subprocess.run(['gcloud', 'config', 'set', 'project', project_id], 
                         check=True, capture_output=True)
            
            # الحصول على التوكن
            result = subprocess.run(['gcloud', 'auth', 'application-default', 'print-access-token'], 
                                  capture_output=True, text=True, check=True)
            
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            logger.error(f"فشل في الحصول على Access Token: {e}")
            return None
    
    def generate_video(self, prompt: str, project_id: str, access_token: str) -> Dict:
        """إنتاج فيديو باستخدام Veo 3"""
        url = f"https://us-central1-aiplatform.googleapis.com/v1/projects/{project_id}/locations/us-central1/publishers/google/models/veo-3:generateVideo"
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "instances": [{
                "prompt": prompt,
                "parameters": {
                    "duration": "15s",
                    "aspect_ratio": "9:16",
                    "quality": "high",
                    "seed": int(time.time())  # للحصول على نتائج متنوعة
                }
            }]
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=300)
            response.raise_for_status()
            
            result = response.json()
            logger.info("تم إنتاج الفيديو بنجاح")
            
            # تحديث عداد الفيديوهات
            self.config["video_generation_count"] += 1
            self.save_config()
            
            return {
                "success": True,
                "data": result,
                "video_url": self.extract_video_url(result),
                "generation_time": datetime.now().isoformat()
            }
            
        except requests.exceptions.RequestException as e:
            logger.error(f"فشل في إنتاج الفيديو: {e}")
            return {
                "success": False,
                "error": str(e),
                "generation_time": datetime.now().isoformat()
            }
    
    def extract_video_url(self, veo_response: Dict) -> Optional[str]:
        """استخراج رابط الفيديو من استجابة Veo"""
        try:
            predictions = veo_response.get("predictions", [])
            if predictions:
                video_uri = predictions[0].get("video_uri")
                if video_uri:
                    # تحويل GCS URI إلى رابط قابل للتحميل
                    return self.convert_gcs_to_download_url(video_uri)
            return None
        except Exception as e:
            logger.error(f"فشل في استخراج رابط الفيديو: {e}")
            return None
    
    def convert_gcs_to_download_url(self, gcs_uri: str) -> str:
        """تحويل GCS URI إلى رابط تحميل مباشر"""
        # استخراج اسم الـ bucket واسم الملف
        parts = gcs_uri.replace("gs://", "").split("/")
        bucket_name = parts[0]
        file_name = "/".join(parts[1:])
        
        # إنشاء رابط التحميل المباشر
        encoded_file_name = requests.utils.quote(file_name, safe='')
        return f"https://storage.googleapis.com/download/storage/v1/b/{bucket_name}/o/{encoded_file_name}?alt=media"
    
    def start_new_lab_session(self, lab_credentials: Dict):
        """بدء جلسة مختبر جديدة"""
        self.current_lab = lab_credentials
        self.config["current_lab_start_time"] = datetime.now().isoformat()
        self.config["labs_used"].append({
            "project_id": lab_credentials.get("project_id"),
            "start_time": self.config["current_lab_start_time"],
            "username": lab_credentials.get("username")
        })
        self.save_config()
        logger.info(f"تم بدء جلسة مختبر جديدة: {lab_credentials.get('project_id')}")
    
    def stop_current_lab(self):
        """إيقاف المختبر الحالي"""
        if self.current_lab:
            logger.info(f"تم إيقاف المختبر: {self.current_lab.get('project_id')}")
            self.current_lab = None
            self.config["current_lab_start_time"] = None
            self.save_config()
    
    def get_lab_status(self) -> Dict:
        """الحصول على حالة المختبر الحالي"""
        if not self.current_lab:
            return {"status": "no_active_lab", "message": "لا يوجد مختبر نشط"}
        
        if self.is_lab_expired():
            return {"status": "expired", "message": "انتهت صلاحية المختبر الحالي"}
        
        start_time = datetime.fromisoformat(self.config["current_lab_start_time"])
        elapsed = (datetime.now() - start_time).total_seconds() / 60
        remaining = self.config["max_lab_duration"] - elapsed
        
        return {
            "status": "active",
            "project_id": self.current_lab.get("project_id"),
            "elapsed_minutes": round(elapsed, 1),
            "remaining_minutes": round(remaining, 1),
            "videos_generated": self.config["video_generation_count"]
        }
    
    def create_video_with_auto_management(self, prompt: str, lab_credentials: Optional[Dict] = None) -> Dict:
        """إنتاج فيديو مع إدارة تلقائية للمختبرات"""
        # فحص حالة المختبر الحالي
        if self.is_lab_expired() or not self.current_lab:
            if lab_credentials:
                self.start_new_lab_session(lab_credentials)
            else:
                return {
                    "success": False,
                    "error": "يحتاج إلى بيانات مختبر جديد",
                    "message": "انتهت صلاحية المختبر الحالي أو لا يوجد مختبر نشط"
                }
        
        # الحصول على Access Token
        access_token = self.get_access_token(self.current_lab["project_id"])
        if not access_token:
            return {
                "success": False,
                "error": "فشل في الحصول على Access Token"
            }
        
        # إنتاج الفيديو
        result = self.generate_video(prompt, self.current_lab["project_id"], access_token)
        
        # إيقاف المختبر بعد الإنتاج لتوفير الوقت
        self.stop_current_lab()
        
        return result

# مثال على الاستخدام
if __name__ == "__main__":
    manager = Veo3Manager()
    
    # بيانات مختبر تجريبية (يجب الحصول عليها من Skills Boost)
    lab_creds = {
        "username": "<EMAIL>",
        "password": "xxxxxxxxxx",
        "project_id": "qwiklabs-gcp-01-xxxxxxxx",
        "region": "us-central1"
    }
    
    # prompt للفيديو
    video_prompt = """A professional chef's hand holding a sharp kitchen knife, positioned above a wooden cutting board. On the cutting board sits a golden apple. The scene is well-lit with dramatic lighting. The chef slowly brings the knife down and cuts the apple perfectly in half, revealing the inside. The cut is clean and precise. Shot in 4K, cinematic style, slow motion effect."""
    
    # إنتاج الفيديو
    result = manager.create_video_with_auto_management(video_prompt, lab_creds)
    print(json.dumps(result, ensure_ascii=False, indent=2))

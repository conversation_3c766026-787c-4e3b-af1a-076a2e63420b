#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import sys
import time
import requests
import os

def run_command(command):
    """تنفيذ أمر وإرجاع النتيجة"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_n8n_status():
    """التحقق من حالة n8n"""
    # التحقق من Docker container
    success, output, error = run_command("docker ps --filter name=n8n --format '{{.Names}}\t{{.Status}}'")
    
    if success and "n8n" in output and "Up" in output:
        # التحقق من استجابة n8n
        try:
            response = requests.get("http://localhost:5678", timeout=3)
            if response.status_code == 200:
                return "running"
        except:
            pass
        return "starting"
    else:
        return "stopped"

def start_n8n():
    """تشغيل n8n"""
    print("🚀 تشغيل n8n...")
    success, output, error = run_command("docker-compose up -d")
    
    if success:
        print("✅ تم بدء التشغيل")
        print("⏳ انتظار...")
        
        # انتظار حتى يصبح n8n جاهز
        for i in range(20):
            time.sleep(3)
            status = check_n8n_status()
            if status == "running":
                print("✅ n8n جاهز!")
                print("🌐 افتح المتصفح على: http://localhost:5678")
                return True
            elif status == "starting":
                print(f"🔄 لا يزال يبدأ... ({i*3}/60 ثانية)")
            else:
                break
        
        print("⚠️ n8n بدأ ولكن قد يحتاج وقت إضافي")
        return True
    else:
        print(f"❌ فشل في التشغيل: {error}")
        return False

def stop_n8n():
    """إيقاف n8n"""
    print("⏹️ إيقاف n8n...")
    success, output, error = run_command("docker-compose down")
    
    if success:
        print("✅ تم إيقاف n8n (البيانات محفوظة)")
        return True
    else:
        print(f"❌ فشل في الإيقاف: {error}")
        return False

def show_status():
    """عرض حالة n8n"""
    status = check_n8n_status()
    
    if status == "running":
        print("✅ n8n يعمل بشكل طبيعي")
        print("🌐 الرابط: http://localhost:5678")
    elif status == "starting":
        print("🔄 n8n يبدأ التشغيل...")
    else:
        print("⏹️ n8n متوقف")
    
    return status

def show_logs():
    """عرض سجلات n8n"""
    print("📋 آخر 20 سطر من سجلات n8n:")
    print("=" * 50)
    success, output, error = run_command("docker logs n8n --tail 20")
    if success:
        print(output)
    else:
        print(f"❌ فشل في عرض السجلات: {error}")

def open_browser():
    """فتح n8n في المتصفح"""
    import webbrowser
    try:
        webbrowser.open("http://localhost:5678")
        print("🌐 تم فتح المتصفح")
    except Exception as e:
        print(f"❌ فشل في فتح المتصفح: {e}")
        print("افتح المتصفح يدوياً على: http://localhost:5678")

def main():
    print("🎛️ مدير n8n البسيط")
    print("=" * 30)
    
    while True:
        print("\nالخيارات المتاحة:")
        print("1. ▶️  تشغيل n8n")
        print("2. ⏹️  إيقاف n8n")
        print("3. 📊 عرض الحالة")
        print("4. 📋 عرض السجلات")
        print("5. 🌐 فتح في المتصفح")
        print("6. ❌ خروج")
        
        try:
            choice = input("\nاختر رقم (1-6): ").strip()
            
            if choice == "1":
                start_n8n()
            elif choice == "2":
                stop_n8n()
            elif choice == "3":
                show_status()
            elif choice == "4":
                show_logs()
            elif choice == "5":
                status = check_n8n_status()
                if status == "running":
                    open_browser()
                else:
                    print("❌ n8n غير يعمل. شغله أولاً.")
            elif choice == "6":
                print("👋 شكراً لاستخدام مدير n8n")
                break
            else:
                print("❌ اختيار غير صحيح")
                
        except KeyboardInterrupt:
            print("\n👋 شكراً لاستخدام مدير n8n")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()

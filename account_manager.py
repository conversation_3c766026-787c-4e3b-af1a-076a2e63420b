#!/usr/bin/env python3
"""
مدير الحسابات المتعددة - إدارة حسابات Google Cloud Skills Boost و TikTok
"""

import json
import time
import random
import requests
import subprocess
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass
from pathlib import Path

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class CloudLabAccount:
    """بيانات حساب Google Cloud Skills Boost"""
    username: str
    password: str
    project_id: str
    region: str
    created_at: str
    expires_at: str
    status: str = "active"  # active, expired, used
    videos_generated: int = 0

@dataclass
class TikTokAccount:
    """بيانات حساب TikTok"""
    username: str
    password: str
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    videos_uploaded: int = 0
    last_upload: Optional[str] = None
    status: str = "active"  # active, suspended, limited

class AccountManager:
    def __init__(self, config_file: str = "accounts_config.json"):
        self.config_file = Path(config_file)
        self.cloud_accounts: List[CloudLabAccount] = []
        self.tiktok_accounts: List[TikTokAccount] = []
        self.current_cloud_account = 0
        self.current_tiktok_account = 0
        self.load_accounts()
    
    def load_accounts(self):
        """تحميل بيانات الحسابات من الملف"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # تحميل حسابات Cloud
                for account_data in data.get('cloud_accounts', []):\n                    self.cloud_accounts.append(CloudLabAccount(**account_data))\n                \n                # تحميل حسابات TikTok\n                for account_data in data.get('tiktok_accounts', []):\n                    self.tiktok_accounts.append(TikTokAccount(**account_data))\n                \n                self.current_cloud_account = data.get('current_cloud_account', 0)\n                self.current_tiktok_account = data.get('current_tiktok_account', 0)\n                \n                logger.info(f\"تم تحميل {len(self.cloud_accounts)} حساب Cloud و {len(self.tiktok_accounts)} حساب TikTok\")\n            except Exception as e:\n                logger.error(f\"فشل في تحميل الحسابات: {e}\")\n                self.initialize_empty_config()\n        else:\n            self.initialize_empty_config()\n    \n    def initialize_empty_config(self):\n        \"\"\"إنشاء ملف إعدادات فارغ\"\"\"\n        self.cloud_accounts = []\n        self.tiktok_accounts = []\n        self.save_accounts()\n    \n    def save_accounts(self):\n        \"\"\"حفظ بيانات الحسابات في الملف\"\"\"\n        data = {\n            'cloud_accounts': [account.__dict__ for account in self.cloud_accounts],\n            'tiktok_accounts': [account.__dict__ for account in self.tiktok_accounts],\n            'current_cloud_account': self.current_cloud_account,\n            'current_tiktok_account': self.current_tiktok_account,\n            'last_updated': datetime.now().isoformat()\n        }\n        \n        with open(self.config_file, 'w', encoding='utf-8') as f:\n            json.dump(data, f, ensure_ascii=False, indent=2)\n    \n    def add_cloud_account(self, username: str, password: str, project_id: str, region: str = \"us-central1\"):\n        \"\"\"إضافة حساب Cloud جديد\"\"\"\n        account = CloudLabAccount(\n            username=username,\n            password=password,\n            project_id=project_id,\n            region=region,\n            created_at=datetime.now().isoformat(),\n            expires_at=(datetime.now() + timedelta(minutes=45)).isoformat()\n        )\n        \n        self.cloud_accounts.append(account)\n        self.save_accounts()\n        logger.info(f\"تم إضافة حساب Cloud جديد: {project_id}\")\n    \n    def add_tiktok_account(self, username: str, password: str, access_token: Optional[str] = None):\n        \"\"\"إضافة حساب TikTok جديد\"\"\"\n        account = TikTokAccount(\n            username=username,\n            password=password,\n            access_token=access_token\n        )\n        \n        self.tiktok_accounts.append(account)\n        self.save_accounts()\n        logger.info(f\"تم إضافة حساب TikTok جديد: {username}\")\n    \n    def get_active_cloud_account(self) -> Optional[CloudLabAccount]:\n        \"\"\"الحصول على حساب Cloud نشط\"\"\"\n        # تنظيف الحسابات المنتهية الصلاحية\n        self.cleanup_expired_accounts()\n        \n        if not self.cloud_accounts:\n            logger.warning(\"لا توجد حسابات Cloud متاحة\")\n            return None\n        \n        # البحث عن حساب نشط\n        for i, account in enumerate(self.cloud_accounts):\n            if account.status == \"active\" and not self.is_account_expired(account):\n                self.current_cloud_account = i\n                return account\n        \n        logger.warning(\"لا توجد حسابات Cloud نشطة\")\n        return None\n    \n    def get_active_tiktok_account(self) -> Optional[TikTokAccount]:\n        \"\"\"الحصول على حساب TikTok نشط\"\"\"\n        if not self.tiktok_accounts:\n            logger.warning(\"لا توجد حسابات TikTok متاحة\")\n            return None\n        \n        # دوران الحسابات لتوزيع الحمل\n        account = self.tiktok_accounts[self.current_tiktok_account]\n        \n        # التحقق من حالة الحساب\n        if account.status != \"active\":\n            # البحث عن حساب نشط آخر\n            for i, acc in enumerate(self.tiktok_accounts):\n                if acc.status == \"active\":\n                    self.current_tiktok_account = i\n                    return acc\n            \n            logger.warning(\"لا توجد حسابات TikTok نشطة\")\n            return None\n        \n        return account\n    \n    def rotate_tiktok_account(self):\n        \"\"\"تدوير حساب TikTok للحساب التالي\"\"\"\n        if len(self.tiktok_accounts) > 1:\n            self.current_tiktok_account = (self.current_tiktok_account + 1) % len(self.tiktok_accounts)\n            self.save_accounts()\n    \n    def is_account_expired(self, account: CloudLabAccount) -> bool:\n        \"\"\"فحص انتهاء صلاحية حساب Cloud\"\"\"\n        expires_at = datetime.fromisoformat(account.expires_at)\n        return datetime.now() > expires_at\n    \n    def cleanup_expired_accounts(self):\n        \"\"\"تنظيف الحسابات المنتهية الصلاحية\"\"\"\n        before_count = len(self.cloud_accounts)\n        self.cloud_accounts = [\n            account for account in self.cloud_accounts \n            if not self.is_account_expired(account) or account.status != \"expired\"\n        ]\n        \n        removed_count = before_count - len(self.cloud_accounts)\n        if removed_count > 0:\n            logger.info(f\"تم إزالة {removed_count} حساب منتهي الصلاحية\")\n            self.save_accounts()\n    \n    def mark_account_used(self, account: CloudLabAccount):\n        \"\"\"تمييز حساب Cloud كمستخدم\"\"\"\n        account.videos_generated += 1\n        account.status = \"used\"\n        self.save_accounts()\n    \n    def mark_tiktok_upload(self, account: TikTokAccount):\n        \"\"\"تسجيل رفع فيديو على TikTok\"\"\"\n        account.videos_uploaded += 1\n        account.last_upload = datetime.now().isoformat()\n        self.save_accounts()\n    \n    def get_account_stats(self) -> Dict:\n        \"\"\"الحصول على إحصائيات الحسابات\"\"\"\n        active_cloud = len([acc for acc in self.cloud_accounts if acc.status == \"active\"])\n        active_tiktok = len([acc for acc in self.tiktok_accounts if acc.status == \"active\"])\n        \n        total_videos_generated = sum(acc.videos_generated for acc in self.cloud_accounts)\n        total_videos_uploaded = sum(acc.videos_uploaded for acc in self.tiktok_accounts)\n        \n        return {\n            \"cloud_accounts\": {\n                \"total\": len(self.cloud_accounts),\n                \"active\": active_cloud,\n                \"expired\": len(self.cloud_accounts) - active_cloud\n            },\n            \"tiktok_accounts\": {\n                \"total\": len(self.tiktok_accounts),\n                \"active\": active_tiktok,\n                \"suspended\": len(self.tiktok_accounts) - active_tiktok\n            },\n            \"production\": {\n                \"videos_generated\": total_videos_generated,\n                \"videos_uploaded\": total_videos_uploaded,\n                \"success_rate\": (total_videos_uploaded / max(total_videos_generated, 1)) * 100\n            }\n        }\n    \n    def auto_create_cloud_lab(self) -> Optional[CloudLabAccount]:\n        \"\"\"إنشاء مختبر Cloud جديد تلقائياً (يتطلب تطوير إضافي)\"\"\"\n        # هذه الوظيفة تحتاج إلى تطوير مع Selenium أو APIs\n        # لأتمتة عملية إنشاء المختبرات الجديدة\n        logger.info(\"محاولة إنشاء مختبر Cloud جديد...\")\n        \n        # مؤقتاً: إرجاع None\n        # في التطبيق الحقيقي، هنا سيكون كود أتمتة إنشاء المختبر\n        return None\n    \n    def health_check(self) -> Dict:\n        \"\"\"فحص صحة النظام\"\"\"\n        stats = self.get_account_stats()\n        \n        health = {\n            \"status\": \"healthy\",\n            \"issues\": [],\n            \"recommendations\": []\n        }\n        \n        # فحص توفر حسابات Cloud\n        if stats[\"cloud_accounts\"][\"active\"] == 0:\n            health[\"status\"] = \"warning\"\n            health[\"issues\"].append(\"لا توجد حسابات Cloud نشطة\")\n            health[\"recommendations\"].append(\"إضافة حسابات Cloud جديدة\")\n        \n        # فحص توفر حسابات TikTok\n        if stats[\"tiktok_accounts\"][\"active\"] == 0:\n            health[\"status\"] = \"critical\"\n            health[\"issues\"].append(\"لا توجد حسابات TikTok نشطة\")\n            health[\"recommendations\"].append(\"إضافة حسابات TikTok جديدة\")\n        \n        # فحص معدل النجاح\n        if stats[\"production\"][\"success_rate\"] < 50:\n            health[\"status\"] = \"warning\"\n            health[\"issues\"].append(\"معدل نجاح منخفض في الرفع\")\n            health[\"recommendations\"].append(\"فحص حالة حسابات TikTok\")\n        \n        return health\n\n# مثال على الاستخدام\nif __name__ == \"__main__\":\n    manager = AccountManager()\n    \n    # إضافة حسابات تجريبية\n    # manager.add_cloud_account(\n    #     \"<EMAIL>\",\n    #     \"password123\",\n    #     \"qwiklabs-gcp-01-xxxxxxxx\"\n    # )\n    \n    # manager.add_tiktok_account(\n    #     \"your_tiktok_username\",\n    #     \"your_tiktok_password\"\n    # )\n    \n    # عرض الإحصائيات\n    stats = manager.get_account_stats()\n    print(json.dumps(stats, ensure_ascii=False, indent=2))\n    \n    # فحص الصحة\n    health = manager.health_check()\n    print(json.dumps(health, ensure_ascii=False, indent=2))
